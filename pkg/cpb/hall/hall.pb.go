// 世界服协议

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v5.27.0
// source: hall.proto

package hallPB

import (
	common "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 请求玩家所在房间信息(用于断线重连)
type GetRoomInfoReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GetRoomInfoReq) Reset() {
	*x = GetRoomInfoReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRoomInfoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRoomInfoReq) ProtoMessage() {}

func (x *GetRoomInfoReq) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRoomInfoReq.ProtoReflect.Descriptor instead.
func (*GetRoomInfoReq) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{0}
}

// 请求玩家所在房间信息(用于断线重连)响应
type GetRoomInfoRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret      *common.Result   `protobuf:"bytes,1,opt,name=ret,proto3" json:"ret,omitempty"`                           // 结果 是否已经在房间中
	RoomInfo *common.RoomInfo `protobuf:"bytes,2,opt,name=room_info,json=roomInfo,proto3" json:"room_info,omitempty"` // 房间信息 room_id为空代表不在房间中
}

func (x *GetRoomInfoRsp) Reset() {
	*x = GetRoomInfoRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRoomInfoRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRoomInfoRsp) ProtoMessage() {}

func (x *GetRoomInfoRsp) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRoomInfoRsp.ProtoReflect.Descriptor instead.
func (*GetRoomInfoRsp) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{1}
}

func (x *GetRoomInfoRsp) GetRet() *common.Result {
	if x != nil {
		return x.Ret
	}
	return nil
}

func (x *GetRoomInfoRsp) GetRoomInfo() *common.RoomInfo {
	if x != nil {
		return x.RoomInfo
	}
	return nil
}

// 请求上次游戏信息
type GetLastGameInfoReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GetLastGameInfoReq) Reset() {
	*x = GetLastGameInfoReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLastGameInfoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLastGameInfoReq) ProtoMessage() {}

func (x *GetLastGameInfoReq) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLastGameInfoReq.ProtoReflect.Descriptor instead.
func (*GetLastGameInfoReq) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{2}
}

// 请求上次游戏信息响应
type GetLastGameInfoRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret      *common.Result   `protobuf:"bytes,1,opt,name=ret,proto3" json:"ret,omitempty"`                           // 结果
	LastGame *common.RoomInfo `protobuf:"bytes,2,opt,name=last_game,json=lastGame,proto3" json:"last_game,omitempty"` // 上次游戏信息
}

func (x *GetLastGameInfoRsp) Reset() {
	*x = GetLastGameInfoRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLastGameInfoRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLastGameInfoRsp) ProtoMessage() {}

func (x *GetLastGameInfoRsp) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLastGameInfoRsp.ProtoReflect.Descriptor instead.
func (*GetLastGameInfoRsp) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{3}
}

func (x *GetLastGameInfoRsp) GetRet() *common.Result {
	if x != nil {
		return x.Ret
	}
	return nil
}

func (x *GetLastGameInfoRsp) GetLastGame() *common.RoomInfo {
	if x != nil {
		return x.LastGame
	}
	return nil
}

// 进入钓场请求
type EnterFisheryReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PondId   int64            `protobuf:"varint,1,opt,name=pond_id,json=pondId,proto3" json:"pond_id,omitempty"`                             // 钓场id
	GameType common.GAME_TYPE `protobuf:"varint,2,opt,name=game_type,json=gameType,proto3,enum=common.GAME_TYPE" json:"game_type,omitempty"` // 游戏类型
	RoomType common.ROOM_TYPE `protobuf:"varint,3,opt,name=room_type,json=roomType,proto3,enum=common.ROOM_TYPE" json:"room_type,omitempty"` // 房间类型
	SpotId   int32            `protobuf:"varint,4,opt,name=spot_id,json=spotId,proto3" json:"spot_id,omitempty"`                             // 钓点id(快速进入时使用)
}

func (x *EnterFisheryReq) Reset() {
	*x = EnterFisheryReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EnterFisheryReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EnterFisheryReq) ProtoMessage() {}

func (x *EnterFisheryReq) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EnterFisheryReq.ProtoReflect.Descriptor instead.
func (*EnterFisheryReq) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{4}
}

func (x *EnterFisheryReq) GetPondId() int64 {
	if x != nil {
		return x.PondId
	}
	return 0
}

func (x *EnterFisheryReq) GetGameType() common.GAME_TYPE {
	if x != nil {
		return x.GameType
	}
	return common.GAME_TYPE(0)
}

func (x *EnterFisheryReq) GetRoomType() common.ROOM_TYPE {
	if x != nil {
		return x.RoomType
	}
	return common.ROOM_TYPE(0)
}

func (x *EnterFisheryReq) GetSpotId() int32 {
	if x != nil {
		return x.SpotId
	}
	return 0
}

// 进入钓点响应
type EnterFisheryRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret      *common.Result   `protobuf:"bytes,1,opt,name=ret,proto3" json:"ret,omitempty"`                           // 结果
	PondId   int64            `protobuf:"varint,2,opt,name=pond_id,json=pondId,proto3" json:"pond_id,omitempty"`      // 钓场id
	RoomInfo *common.RoomInfo `protobuf:"bytes,3,opt,name=room_info,json=roomInfo,proto3" json:"room_info,omitempty"` // 房间信息
	SpotId   int32            `protobuf:"varint,4,opt,name=spot_id,json=spotId,proto3" json:"spot_id,omitempty"`      // 钓点id
}

func (x *EnterFisheryRsp) Reset() {
	*x = EnterFisheryRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EnterFisheryRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EnterFisheryRsp) ProtoMessage() {}

func (x *EnterFisheryRsp) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EnterFisheryRsp.ProtoReflect.Descriptor instead.
func (*EnterFisheryRsp) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{5}
}

func (x *EnterFisheryRsp) GetRet() *common.Result {
	if x != nil {
		return x.Ret
	}
	return nil
}

func (x *EnterFisheryRsp) GetPondId() int64 {
	if x != nil {
		return x.PondId
	}
	return 0
}

func (x *EnterFisheryRsp) GetRoomInfo() *common.RoomInfo {
	if x != nil {
		return x.RoomInfo
	}
	return nil
}

func (x *EnterFisheryRsp) GetSpotId() int32 {
	if x != nil {
		return x.SpotId
	}
	return 0
}

// 查询指定道具信息
type GetItemInfoReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ItemList []int64 `protobuf:"varint,1,rep,packed,name=item_list,json=itemList,proto3" json:"item_list,omitempty"` // 道具id列表
}

func (x *GetItemInfoReq) Reset() {
	*x = GetItemInfoReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetItemInfoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetItemInfoReq) ProtoMessage() {}

func (x *GetItemInfoReq) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetItemInfoReq.ProtoReflect.Descriptor instead.
func (*GetItemInfoReq) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{6}
}

func (x *GetItemInfoReq) GetItemList() []int64 {
	if x != nil {
		return x.ItemList
	}
	return nil
}

// 查询指定道具信息响应
type GetItemInfoRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret      *common.Result     `protobuf:"bytes,1,opt,name=ret,proto3" json:"ret,omitempty"`                                   // 结果
	ItemList []int64            `protobuf:"varint,2,rep,packed,name=item_list,json=itemList,proto3" json:"item_list,omitempty"` // 道具id列表
	ItemInfo []*common.ItemInfo `protobuf:"bytes,3,rep,name=item_info,json=itemInfo,proto3" json:"item_info,omitempty"`         // 道具信息
}

func (x *GetItemInfoRsp) Reset() {
	*x = GetItemInfoRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetItemInfoRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetItemInfoRsp) ProtoMessage() {}

func (x *GetItemInfoRsp) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetItemInfoRsp.ProtoReflect.Descriptor instead.
func (*GetItemInfoRsp) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{7}
}

func (x *GetItemInfoRsp) GetRet() *common.Result {
	if x != nil {
		return x.Ret
	}
	return nil
}

func (x *GetItemInfoRsp) GetItemList() []int64 {
	if x != nil {
		return x.ItemList
	}
	return nil
}

func (x *GetItemInfoRsp) GetItemInfo() []*common.ItemInfo {
	if x != nil {
		return x.ItemInfo
	}
	return nil
}

// 根据道具类型查询道具信息
type GetItemInfoByTypeReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ItemType common.ITEM_TYPE `protobuf:"varint,1,opt,name=item_type,json=itemType,proto3,enum=common.ITEM_TYPE" json:"item_type,omitempty"` // 道具类型(-1 查询全部)
}

func (x *GetItemInfoByTypeReq) Reset() {
	*x = GetItemInfoByTypeReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetItemInfoByTypeReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetItemInfoByTypeReq) ProtoMessage() {}

func (x *GetItemInfoByTypeReq) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetItemInfoByTypeReq.ProtoReflect.Descriptor instead.
func (*GetItemInfoByTypeReq) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{8}
}

func (x *GetItemInfoByTypeReq) GetItemType() common.ITEM_TYPE {
	if x != nil {
		return x.ItemType
	}
	return common.ITEM_TYPE(0)
}

// 根据道具类型查询道具信息响应
type GetItemInfoByTypeRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret      *common.Result     `protobuf:"bytes,1,opt,name=ret,proto3" json:"ret,omitempty"`                           // 结果
	ItemInfo []*common.ItemInfo `protobuf:"bytes,2,rep,name=item_info,json=itemInfo,proto3" json:"item_info,omitempty"` // 道具信息
}

func (x *GetItemInfoByTypeRsp) Reset() {
	*x = GetItemInfoByTypeRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetItemInfoByTypeRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetItemInfoByTypeRsp) ProtoMessage() {}

func (x *GetItemInfoByTypeRsp) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetItemInfoByTypeRsp.ProtoReflect.Descriptor instead.
func (*GetItemInfoByTypeRsp) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{9}
}

func (x *GetItemInfoByTypeRsp) GetRet() *common.Result {
	if x != nil {
		return x.Ret
	}
	return nil
}

func (x *GetItemInfoByTypeRsp) GetItemInfo() []*common.ItemInfo {
	if x != nil {
		return x.ItemInfo
	}
	return nil
}

// 更新玩家道具推送
type UpdateItemInfoNtf struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RewardInfo *common.Reward      `protobuf:"bytes,1,opt,name=reward_info,json=rewardInfo,proto3" json:"reward_info,omitempty"`   // 道具信息
	Storage    common.STORAGE_TYPE `protobuf:"varint,2,opt,name=storage,proto3,enum=common.STORAGE_TYPE" json:"storage,omitempty"` // 存储位置(扣除来源)
}

func (x *UpdateItemInfoNtf) Reset() {
	*x = UpdateItemInfoNtf{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateItemInfoNtf) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateItemInfoNtf) ProtoMessage() {}

func (x *UpdateItemInfoNtf) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateItemInfoNtf.ProtoReflect.Descriptor instead.
func (*UpdateItemInfoNtf) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{10}
}

func (x *UpdateItemInfoNtf) GetRewardInfo() *common.Reward {
	if x != nil {
		return x.RewardInfo
	}
	return nil
}

func (x *UpdateItemInfoNtf) GetStorage() common.STORAGE_TYPE {
	if x != nil {
		return x.Storage
	}
	return common.STORAGE_TYPE(0)
}

// 商品购买信息请求(针对限制商品)
type GetGoodsBuyInfoReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GoodsList []int64 `protobuf:"varint,1,rep,packed,name=goods_list,json=goodsList,proto3" json:"goods_list,omitempty"` // 商品id列表
}

func (x *GetGoodsBuyInfoReq) Reset() {
	*x = GetGoodsBuyInfoReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetGoodsBuyInfoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetGoodsBuyInfoReq) ProtoMessage() {}

func (x *GetGoodsBuyInfoReq) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetGoodsBuyInfoReq.ProtoReflect.Descriptor instead.
func (*GetGoodsBuyInfoReq) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{11}
}

func (x *GetGoodsBuyInfoReq) GetGoodsList() []int64 {
	if x != nil {
		return x.GoodsList
	}
	return nil
}

// 商品购买信息响应(针对限制商品)
type GetGoodsBuyInfoRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret     *common.Result         `protobuf:"bytes,1,opt,name=ret,proto3" json:"ret,omitempty"`                        // 结果
	BuyInfo []*common.GoodsBuyInfo `protobuf:"bytes,2,rep,name=buy_info,json=buyInfo,proto3" json:"buy_info,omitempty"` // 商品购买信息
}

func (x *GetGoodsBuyInfoRsp) Reset() {
	*x = GetGoodsBuyInfoRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetGoodsBuyInfoRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetGoodsBuyInfoRsp) ProtoMessage() {}

func (x *GetGoodsBuyInfoRsp) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetGoodsBuyInfoRsp.ProtoReflect.Descriptor instead.
func (*GetGoodsBuyInfoRsp) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{12}
}

func (x *GetGoodsBuyInfoRsp) GetRet() *common.Result {
	if x != nil {
		return x.Ret
	}
	return nil
}

func (x *GetGoodsBuyInfoRsp) GetBuyInfo() []*common.GoodsBuyInfo {
	if x != nil {
		return x.BuyInfo
	}
	return nil
}

// 商城购买请求
type StoreBuyReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StoreBuyId int64                   `protobuf:"varint,1,opt,name=store_buy_id,json=storeBuyId,proto3" json:"store_buy_id,omitempty"`                         // 商城购买id
	Count      int32                   `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`                                                       // 数量
	StyleType  common.STORE_SHOW_STYLE `protobuf:"varint,3,opt,name=style_type,json=styleType,proto3,enum=common.STORE_SHOW_STYLE" json:"style_type,omitempty"` // 商品样式(大厅还是房间)
}

func (x *StoreBuyReq) Reset() {
	*x = StoreBuyReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StoreBuyReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StoreBuyReq) ProtoMessage() {}

func (x *StoreBuyReq) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StoreBuyReq.ProtoReflect.Descriptor instead.
func (*StoreBuyReq) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{13}
}

func (x *StoreBuyReq) GetStoreBuyId() int64 {
	if x != nil {
		return x.StoreBuyId
	}
	return 0
}

func (x *StoreBuyReq) GetCount() int32 {
	if x != nil {
		return x.Count
	}
	return 0
}

func (x *StoreBuyReq) GetStyleType() common.STORE_SHOW_STYLE {
	if x != nil {
		return x.StyleType
	}
	return common.STORE_SHOW_STYLE(0)
}

// 商城购买响应
type StoreBuyRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret        *common.Result `protobuf:"bytes,1,opt,name=ret,proto3" json:"ret,omitempty"`                                    // 结果
	StoreBuyId int64          `protobuf:"varint,2,opt,name=store_buy_id,json=storeBuyId,proto3" json:"store_buy_id,omitempty"` // 商城购买id
	Count      int32          `protobuf:"varint,3,opt,name=count,proto3" json:"count,omitempty"`                               // 数量
	RewardInfo *common.Reward `protobuf:"bytes,4,opt,name=reward_info,json=rewardInfo,proto3" json:"reward_info,omitempty"`    // 奖励信息
}

func (x *StoreBuyRsp) Reset() {
	*x = StoreBuyRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StoreBuyRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StoreBuyRsp) ProtoMessage() {}

func (x *StoreBuyRsp) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StoreBuyRsp.ProtoReflect.Descriptor instead.
func (*StoreBuyRsp) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{14}
}

func (x *StoreBuyRsp) GetRet() *common.Result {
	if x != nil {
		return x.Ret
	}
	return nil
}

func (x *StoreBuyRsp) GetStoreBuyId() int64 {
	if x != nil {
		return x.StoreBuyId
	}
	return 0
}

func (x *StoreBuyRsp) GetCount() int32 {
	if x != nil {
		return x.Count
	}
	return 0
}

func (x *StoreBuyRsp) GetRewardInfo() *common.Reward {
	if x != nil {
		return x.RewardInfo
	}
	return nil
}

// 玩家等级变化通知
type ExpLevelChangeNtf struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CurExp          int64                        `protobuf:"varint,1,opt,name=cur_exp,json=curExp,proto3" json:"cur_exp,omitempty"`                             // 当前经验值
	LevelChangeInfo []*common.ExpLevelChangeInfo `protobuf:"bytes,2,rep,name=level_change_info,json=levelChangeInfo,proto3" json:"level_change_info,omitempty"` // 等级变化信息
}

func (x *ExpLevelChangeNtf) Reset() {
	*x = ExpLevelChangeNtf{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExpLevelChangeNtf) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExpLevelChangeNtf) ProtoMessage() {}

func (x *ExpLevelChangeNtf) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExpLevelChangeNtf.ProtoReflect.Descriptor instead.
func (*ExpLevelChangeNtf) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{15}
}

func (x *ExpLevelChangeNtf) GetCurExp() int64 {
	if x != nil {
		return x.CurExp
	}
	return 0
}

func (x *ExpLevelChangeNtf) GetLevelChangeInfo() []*common.ExpLevelChangeInfo {
	if x != nil {
		return x.LevelChangeInfo
	}
	return nil
}

// 玩家信息查询
type GetPlayerInfoReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PlayerId uint64 `protobuf:"varint,1,opt,name=player_id,json=playerId,proto3" json:"player_id,omitempty"` // 玩家id
}

func (x *GetPlayerInfoReq) Reset() {
	*x = GetPlayerInfoReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPlayerInfoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPlayerInfoReq) ProtoMessage() {}

func (x *GetPlayerInfoReq) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPlayerInfoReq.ProtoReflect.Descriptor instead.
func (*GetPlayerInfoReq) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{16}
}

func (x *GetPlayerInfoReq) GetPlayerId() uint64 {
	if x != nil {
		return x.PlayerId
	}
	return 0
}

// 玩家信息查询响应
type GetPlayerInfoRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PlayerInfo *common.PlayerBaseInfo `protobuf:"bytes,1,opt,name=player_info,json=playerInfo,proto3" json:"player_info,omitempty"` // 玩家信息
}

func (x *GetPlayerInfoRsp) Reset() {
	*x = GetPlayerInfoRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPlayerInfoRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPlayerInfoRsp) ProtoMessage() {}

func (x *GetPlayerInfoRsp) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPlayerInfoRsp.ProtoReflect.Descriptor instead.
func (*GetPlayerInfoRsp) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{17}
}

func (x *GetPlayerInfoRsp) GetPlayerInfo() *common.PlayerBaseInfo {
	if x != nil {
		return x.PlayerInfo
	}
	return nil
}

// 玩家使用道具
type UseItemReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ItemInfo []*common.ItemBase      `protobuf:"bytes,1,rep,name=item_info,json=itemInfo,proto3" json:"item_info,omitempty"`                            // 使用道具信息(扣除)
	SrcType  common.ITEM_SOURCE_TYPE `protobuf:"varint,2,opt,name=src_type,json=srcType,proto3,enum=common.ITEM_SOURCE_TYPE" json:"src_type,omitempty"` // 使用来源(扣除原因)
	Storage  common.STORAGE_TYPE     `protobuf:"varint,3,opt,name=storage,proto3,enum=common.STORAGE_TYPE" json:"storage,omitempty"`                    // 存储位置(扣除来源)
}

func (x *UseItemReq) Reset() {
	*x = UseItemReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UseItemReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UseItemReq) ProtoMessage() {}

func (x *UseItemReq) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UseItemReq.ProtoReflect.Descriptor instead.
func (*UseItemReq) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{18}
}

func (x *UseItemReq) GetItemInfo() []*common.ItemBase {
	if x != nil {
		return x.ItemInfo
	}
	return nil
}

func (x *UseItemReq) GetSrcType() common.ITEM_SOURCE_TYPE {
	if x != nil {
		return x.SrcType
	}
	return common.ITEM_SOURCE_TYPE(0)
}

func (x *UseItemReq) GetStorage() common.STORAGE_TYPE {
	if x != nil {
		return x.Storage
	}
	return common.STORAGE_TYPE(0)
}

// 玩家使用道具
type UseItemRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret *common.Result `protobuf:"bytes,1,opt,name=ret,proto3" json:"ret,omitempty"` // 结果 (具体更新后的数据使用 UpdateItemInfoNtf 推送)
}

func (x *UseItemRsp) Reset() {
	*x = UseItemRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UseItemRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UseItemRsp) ProtoMessage() {}

func (x *UseItemRsp) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UseItemRsp.ProtoReflect.Descriptor instead.
func (*UseItemRsp) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{19}
}

func (x *UseItemRsp) GetRet() *common.Result {
	if x != nil {
		return x.Ret
	}
	return nil
}

// FirstEnterHallReq 首次进入大厅请求
type FirstEnterHallReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IsReg bool `protobuf:"varint,1,opt,name=is_reg,json=isReg,proto3" json:"is_reg,omitempty"` // 是否注册
}

func (x *FirstEnterHallReq) Reset() {
	*x = FirstEnterHallReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FirstEnterHallReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FirstEnterHallReq) ProtoMessage() {}

func (x *FirstEnterHallReq) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FirstEnterHallReq.ProtoReflect.Descriptor instead.
func (*FirstEnterHallReq) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{20}
}

func (x *FirstEnterHallReq) GetIsReg() bool {
	if x != nil {
		return x.IsReg
	}
	return false
}

// FirstEnterHallRsp 首次进入大厅响应
type FirstEnterHallRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PlayerInfo *common.PlayerBaseInfo `protobuf:"bytes,1,opt,name=player_info,json=playerInfo,proto3" json:"player_info,omitempty"` // 玩家信息
	PopupInfo  []*common.AnnPopupInfo `protobuf:"bytes,2,rep,name=popup_info,json=popupInfo,proto3" json:"popup_info,omitempty"`    // 拍脸图
}

func (x *FirstEnterHallRsp) Reset() {
	*x = FirstEnterHallRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FirstEnterHallRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FirstEnterHallRsp) ProtoMessage() {}

func (x *FirstEnterHallRsp) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FirstEnterHallRsp.ProtoReflect.Descriptor instead.
func (*FirstEnterHallRsp) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{21}
}

func (x *FirstEnterHallRsp) GetPlayerInfo() *common.PlayerBaseInfo {
	if x != nil {
		return x.PlayerInfo
	}
	return nil
}

func (x *FirstEnterHallRsp) GetPopupInfo() []*common.AnnPopupInfo {
	if x != nil {
		return x.PopupInfo
	}
	return nil
}

// StoreMultiBuyReq 多商品购买(针对非限制商品)
type StoreMultiBuyReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GoodsList []*common.ItemBase      `protobuf:"bytes,1,rep,name=goods_list,json=goodsList,proto3" json:"goods_list,omitempty"`                               // 商品列表 (里面的itemId是商城购买id)
	StyleType common.STORE_SHOW_STYLE `protobuf:"varint,2,opt,name=style_type,json=styleType,proto3,enum=common.STORE_SHOW_STYLE" json:"style_type,omitempty"` // 商品样式(大厅还是房间)
}

func (x *StoreMultiBuyReq) Reset() {
	*x = StoreMultiBuyReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StoreMultiBuyReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StoreMultiBuyReq) ProtoMessage() {}

func (x *StoreMultiBuyReq) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StoreMultiBuyReq.ProtoReflect.Descriptor instead.
func (*StoreMultiBuyReq) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{22}
}

func (x *StoreMultiBuyReq) GetGoodsList() []*common.ItemBase {
	if x != nil {
		return x.GoodsList
	}
	return nil
}

func (x *StoreMultiBuyReq) GetStyleType() common.STORE_SHOW_STYLE {
	if x != nil {
		return x.StyleType
	}
	return common.STORE_SHOW_STYLE(0)
}

// StoreMultiBuyRsp 多商品购买(针对非限制商品)响应
type StoreMultiBuyRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret        *common.Result     `protobuf:"bytes,1,opt,name=ret,proto3" json:"ret,omitempty"`                                 // 结果
	GoosList   []*common.ItemBase `protobuf:"bytes,2,rep,name=goos_list,json=goosList,proto3" json:"goos_list,omitempty"`       // 商品列表
	RewardInfo *common.Reward     `protobuf:"bytes,3,opt,name=reward_info,json=rewardInfo,proto3" json:"reward_info,omitempty"` // 奖励信息
}

func (x *StoreMultiBuyRsp) Reset() {
	*x = StoreMultiBuyRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StoreMultiBuyRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StoreMultiBuyRsp) ProtoMessage() {}

func (x *StoreMultiBuyRsp) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StoreMultiBuyRsp.ProtoReflect.Descriptor instead.
func (*StoreMultiBuyRsp) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{23}
}

func (x *StoreMultiBuyRsp) GetRet() *common.Result {
	if x != nil {
		return x.Ret
	}
	return nil
}

func (x *StoreMultiBuyRsp) GetGoosList() []*common.ItemBase {
	if x != nil {
		return x.GoosList
	}
	return nil
}

func (x *StoreMultiBuyRsp) GetRewardInfo() *common.Reward {
	if x != nil {
		return x.RewardInfo
	}
	return nil
}

// GetStatListReq 获取用户统计列表请求
type GetStatListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GetStatListReq) Reset() {
	*x = GetStatListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetStatListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetStatListReq) ProtoMessage() {}

func (x *GetStatListReq) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetStatListReq.ProtoReflect.Descriptor instead.
func (*GetStatListReq) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{24}
}

// GetStatListRsp 获取用户统计列表响应
type GetStatListRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret  *common.Result     `protobuf:"bytes,1,opt,name=ret,proto3" json:"ret,omitempty"`
	List []*common.StatInfo `protobuf:"bytes,2,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *GetStatListRsp) Reset() {
	*x = GetStatListRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetStatListRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetStatListRsp) ProtoMessage() {}

func (x *GetStatListRsp) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetStatListRsp.ProtoReflect.Descriptor instead.
func (*GetStatListRsp) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{25}
}

func (x *GetStatListRsp) GetRet() *common.Result {
	if x != nil {
		return x.Ret
	}
	return nil
}

func (x *GetStatListRsp) GetList() []*common.StatInfo {
	if x != nil {
		return x.List
	}
	return nil
}

// StatInfoUpdateNtf 统计信息通知
type StatInfoUpdateNtf struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*common.StatInfo `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *StatInfoUpdateNtf) Reset() {
	*x = StatInfoUpdateNtf{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StatInfoUpdateNtf) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StatInfoUpdateNtf) ProtoMessage() {}

func (x *StatInfoUpdateNtf) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StatInfoUpdateNtf.ProtoReflect.Descriptor instead.
func (*StatInfoUpdateNtf) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{26}
}

func (x *StatInfoUpdateNtf) GetList() []*common.StatInfo {
	if x != nil {
		return x.List
	}
	return nil
}

// GetRodRigInfoReq 竿组信息请求
type GetRodRigInfoReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RigId int32 `protobuf:"varint,1,opt,name=rig_id,json=rigId,proto3" json:"rig_id,omitempty"` // 钓组id(-1 获取全部)
}

func (x *GetRodRigInfoReq) Reset() {
	*x = GetRodRigInfoReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRodRigInfoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRodRigInfoReq) ProtoMessage() {}

func (x *GetRodRigInfoReq) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRodRigInfoReq.ProtoReflect.Descriptor instead.
func (*GetRodRigInfoReq) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{27}
}

func (x *GetRodRigInfoReq) GetRigId() int32 {
	if x != nil {
		return x.RigId
	}
	return 0
}

// GetRodRigInfoRsp 竿组信息响应
type GetRodRigInfoRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret     *common.Result       `protobuf:"bytes,1,opt,name=ret,proto3" json:"ret,omitempty"`                        // 结果
	RigList []*common.RodRigInfo `protobuf:"bytes,2,rep,name=rig_list,json=rigList,proto3" json:"rig_list,omitempty"` // 钓组信息列表
}

func (x *GetRodRigInfoRsp) Reset() {
	*x = GetRodRigInfoRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRodRigInfoRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRodRigInfoRsp) ProtoMessage() {}

func (x *GetRodRigInfoRsp) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRodRigInfoRsp.ProtoReflect.Descriptor instead.
func (*GetRodRigInfoRsp) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{28}
}

func (x *GetRodRigInfoRsp) GetRet() *common.Result {
	if x != nil {
		return x.Ret
	}
	return nil
}

func (x *GetRodRigInfoRsp) GetRigList() []*common.RodRigInfo {
	if x != nil {
		return x.RigList
	}
	return nil
}

// UpdateRodRigInfoReq 更新竿组信息请求
type UpdateRodRigInfoReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RigInfo *common.RodRigInfo `protobuf:"bytes,1,opt,name=rig_info,json=rigInfo,proto3" json:"rig_info,omitempty"` // 竿组信息
}

func (x *UpdateRodRigInfoReq) Reset() {
	*x = UpdateRodRigInfoReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateRodRigInfoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateRodRigInfoReq) ProtoMessage() {}

func (x *UpdateRodRigInfoReq) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateRodRigInfoReq.ProtoReflect.Descriptor instead.
func (*UpdateRodRigInfoReq) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{29}
}

func (x *UpdateRodRigInfoReq) GetRigInfo() *common.RodRigInfo {
	if x != nil {
		return x.RigInfo
	}
	return nil
}

// UpdateRodRigInfoRsp 更新竿组信息响应
type UpdateRodRigInfoRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret     *common.Result     `protobuf:"bytes,1,opt,name=ret,proto3" json:"ret,omitempty"`                        // 结果
	RigInfo *common.RodRigInfo `protobuf:"bytes,2,opt,name=rig_info,json=rigInfo,proto3" json:"rig_info,omitempty"` // 竿组信息
}

func (x *UpdateRodRigInfoRsp) Reset() {
	*x = UpdateRodRigInfoRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateRodRigInfoRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateRodRigInfoRsp) ProtoMessage() {}

func (x *UpdateRodRigInfoRsp) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateRodRigInfoRsp.ProtoReflect.Descriptor instead.
func (*UpdateRodRigInfoRsp) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{30}
}

func (x *UpdateRodRigInfoRsp) GetRet() *common.Result {
	if x != nil {
		return x.Ret
	}
	return nil
}

func (x *UpdateRodRigInfoRsp) GetRigInfo() *common.RodRigInfo {
	if x != nil {
		return x.RigInfo
	}
	return nil
}

// 删除竿组信息请求
type DeleteRodRigInfoReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RigId int32 `protobuf:"varint,1,opt,name=rig_id,json=rigId,proto3" json:"rig_id,omitempty"` // 钓组id
}

func (x *DeleteRodRigInfoReq) Reset() {
	*x = DeleteRodRigInfoReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteRodRigInfoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteRodRigInfoReq) ProtoMessage() {}

func (x *DeleteRodRigInfoReq) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteRodRigInfoReq.ProtoReflect.Descriptor instead.
func (*DeleteRodRigInfoReq) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{31}
}

func (x *DeleteRodRigInfoReq) GetRigId() int32 {
	if x != nil {
		return x.RigId
	}
	return 0
}

// 删除竿组信息响应
type DeleteRodRigInfoRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret   *common.Result `protobuf:"bytes,1,opt,name=ret,proto3" json:"ret,omitempty"`                   // 结果
	RigId int32          `protobuf:"varint,2,opt,name=rig_id,json=rigId,proto3" json:"rig_id,omitempty"` // 钓组id
}

func (x *DeleteRodRigInfoRsp) Reset() {
	*x = DeleteRodRigInfoRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteRodRigInfoRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteRodRigInfoRsp) ProtoMessage() {}

func (x *DeleteRodRigInfoRsp) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteRodRigInfoRsp.ProtoReflect.Descriptor instead.
func (*DeleteRodRigInfoRsp) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{32}
}

func (x *DeleteRodRigInfoRsp) GetRet() *common.Result {
	if x != nil {
		return x.Ret
	}
	return nil
}

func (x *DeleteRodRigInfoRsp) GetRigId() int32 {
	if x != nil {
		return x.RigId
	}
	return 0
}

// 更新杆组信息推送
type UpdateRodRigNtf struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RodInfo []*common.RodBagInfo `protobuf:"bytes,1,rep,name=rod_info,json=rodInfo,proto3" json:"rod_info,omitempty"` // 钓组信息
}

func (x *UpdateRodRigNtf) Reset() {
	*x = UpdateRodRigNtf{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateRodRigNtf) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateRodRigNtf) ProtoMessage() {}

func (x *UpdateRodRigNtf) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateRodRigNtf.ProtoReflect.Descriptor instead.
func (*UpdateRodRigNtf) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{33}
}

func (x *UpdateRodRigNtf) GetRodInfo() []*common.RodBagInfo {
	if x != nil {
		return x.RodInfo
	}
	return nil
}

// 修改道具耐久度请求
type ModifyDurabilityReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RigId  int32           `protobuf:"varint,1,opt,name=rig_id,json=rigId,proto3" json:"rig_id,omitempty"`                                                                               // 钓组id
	Change map[int32]int64 `protobuf:"bytes,2,rep,name=change,proto3" json:"change,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"` // key = sitId，value=扣减值
}

func (x *ModifyDurabilityReq) Reset() {
	*x = ModifyDurabilityReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModifyDurabilityReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModifyDurabilityReq) ProtoMessage() {}

func (x *ModifyDurabilityReq) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModifyDurabilityReq.ProtoReflect.Descriptor instead.
func (*ModifyDurabilityReq) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{34}
}

func (x *ModifyDurabilityReq) GetRigId() int32 {
	if x != nil {
		return x.RigId
	}
	return 0
}

func (x *ModifyDurabilityReq) GetChange() map[int32]int64 {
	if x != nil {
		return x.Change
	}
	return nil
}

// 修改道具耐久度返回
type ModifyDurabilityRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret     *common.Result     `protobuf:"bytes,1,opt,name=ret,proto3" json:"ret,omitempty"`                        // 返回结果
	RodInfo *common.RodBagInfo `protobuf:"bytes,2,opt,name=rod_info,json=rodInfo,proto3" json:"rod_info,omitempty"` // 钓组信息
}

func (x *ModifyDurabilityRsp) Reset() {
	*x = ModifyDurabilityRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModifyDurabilityRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModifyDurabilityRsp) ProtoMessage() {}

func (x *ModifyDurabilityRsp) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModifyDurabilityRsp.ProtoReflect.Descriptor instead.
func (*ModifyDurabilityRsp) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{35}
}

func (x *ModifyDurabilityRsp) GetRet() *common.Result {
	if x != nil {
		return x.Ret
	}
	return nil
}

func (x *ModifyDurabilityRsp) GetRodInfo() *common.RodBagInfo {
	if x != nil {
		return x.RodInfo
	}
	return nil
}

// 维修杆包道具请求
type MaintainRodItemReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RigId int32   `protobuf:"varint,1,opt,name=rig_id,json=rigId,proto3" json:"rig_id,omitempty"` // 钓组id
	SitId []int32 `protobuf:"varint,2,rep,packed,name=sitId,proto3" json:"sitId,omitempty"`       // 钓组位置id
}

func (x *MaintainRodItemReq) Reset() {
	*x = MaintainRodItemReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MaintainRodItemReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MaintainRodItemReq) ProtoMessage() {}

func (x *MaintainRodItemReq) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MaintainRodItemReq.ProtoReflect.Descriptor instead.
func (*MaintainRodItemReq) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{36}
}

func (x *MaintainRodItemReq) GetRigId() int32 {
	if x != nil {
		return x.RigId
	}
	return 0
}

func (x *MaintainRodItemReq) GetSitId() []int32 {
	if x != nil {
		return x.SitId
	}
	return nil
}

// 维修杆包道具返回
type MaintainRodItemRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret     *common.Result     `protobuf:"bytes,1,opt,name=ret,proto3" json:"ret,omitempty"`                        // 返回结果
	RodInfo *common.RodBagInfo `protobuf:"bytes,2,opt,name=rod_info,json=rodInfo,proto3" json:"rod_info,omitempty"` // 钓组信息
	Cost    *common.Reward     `protobuf:"bytes,3,opt,name=cost,proto3" json:"cost,omitempty"`
}

func (x *MaintainRodItemRsp) Reset() {
	*x = MaintainRodItemRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MaintainRodItemRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MaintainRodItemRsp) ProtoMessage() {}

func (x *MaintainRodItemRsp) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MaintainRodItemRsp.ProtoReflect.Descriptor instead.
func (*MaintainRodItemRsp) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{37}
}

func (x *MaintainRodItemRsp) GetRet() *common.Result {
	if x != nil {
		return x.Ret
	}
	return nil
}

func (x *MaintainRodItemRsp) GetRodInfo() *common.RodBagInfo {
	if x != nil {
		return x.RodInfo
	}
	return nil
}

func (x *MaintainRodItemRsp) GetCost() *common.Reward {
	if x != nil {
		return x.Cost
	}
	return nil
}

// 维修仓库道具请求
type MaintainStorageItemReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StoreType  common.STORAGE_TYPE `protobuf:"varint,1,opt,name=store_type,json=storeType,proto3,enum=common.STORAGE_TYPE" json:"store_type,omitempty"` // 仓库类型
	InstanceId []string            `protobuf:"bytes,2,rep,name=instance_id,json=instanceId,proto3" json:"instance_id,omitempty"`                        // 道具id
}

func (x *MaintainStorageItemReq) Reset() {
	*x = MaintainStorageItemReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MaintainStorageItemReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MaintainStorageItemReq) ProtoMessage() {}

func (x *MaintainStorageItemReq) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MaintainStorageItemReq.ProtoReflect.Descriptor instead.
func (*MaintainStorageItemReq) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{38}
}

func (x *MaintainStorageItemReq) GetStoreType() common.STORAGE_TYPE {
	if x != nil {
		return x.StoreType
	}
	return common.STORAGE_TYPE(0)
}

func (x *MaintainStorageItemReq) GetInstanceId() []string {
	if x != nil {
		return x.InstanceId
	}
	return nil
}

// 维修杆包道具返回
type MaintainStorageItemRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret  *common.Result `protobuf:"bytes,1,opt,name=ret,proto3" json:"ret,omitempty"` // 返回结果
	Cost *common.Reward `protobuf:"bytes,3,opt,name=cost,proto3" json:"cost,omitempty"`
}

func (x *MaintainStorageItemRsp) Reset() {
	*x = MaintainStorageItemRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MaintainStorageItemRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MaintainStorageItemRsp) ProtoMessage() {}

func (x *MaintainStorageItemRsp) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MaintainStorageItemRsp.ProtoReflect.Descriptor instead.
func (*MaintainStorageItemRsp) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{39}
}

func (x *MaintainStorageItemRsp) GetRet() *common.Result {
	if x != nil {
		return x.Ret
	}
	return nil
}

func (x *MaintainStorageItemRsp) GetCost() *common.Reward {
	if x != nil {
		return x.Cost
	}
	return nil
}

// 出售道具请求 (仓库/背包)
type SellItemReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StoreType common.STORAGE_TYPE `protobuf:"varint,1,opt,name=store_type,json=storeType,proto3,enum=common.STORAGE_TYPE" json:"store_type,omitempty"`
	List      []*common.ItemBase  `protobuf:"bytes,2,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *SellItemReq) Reset() {
	*x = SellItemReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SellItemReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SellItemReq) ProtoMessage() {}

func (x *SellItemReq) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SellItemReq.ProtoReflect.Descriptor instead.
func (*SellItemReq) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{40}
}

func (x *SellItemReq) GetStoreType() common.STORAGE_TYPE {
	if x != nil {
		return x.StoreType
	}
	return common.STORAGE_TYPE(0)
}

func (x *SellItemReq) GetList() []*common.ItemBase {
	if x != nil {
		return x.List
	}
	return nil
}

type SellItemRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret    *common.Result `protobuf:"bytes,1,opt,name=ret,proto3" json:"ret,omitempty"`
	Reward *common.Reward `protobuf:"bytes,2,opt,name=reward,proto3" json:"reward,omitempty"`
}

func (x *SellItemRsp) Reset() {
	*x = SellItemRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SellItemRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SellItemRsp) ProtoMessage() {}

func (x *SellItemRsp) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SellItemRsp.ProtoReflect.Descriptor instead.
func (*SellItemRsp) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{41}
}

func (x *SellItemRsp) GetRet() *common.Result {
	if x != nil {
		return x.Ret
	}
	return nil
}

func (x *SellItemRsp) GetReward() *common.Reward {
	if x != nil {
		return x.Reward
	}
	return nil
}

// 获取钓组信息
type GetTripRodReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Storage common.STORAGE_TYPE `protobuf:"varint,1,opt,name=storage,proto3,enum=common.STORAGE_TYPE" json:"storage,omitempty"` // 存储类型
}

func (x *GetTripRodReq) Reset() {
	*x = GetTripRodReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[42]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTripRodReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTripRodReq) ProtoMessage() {}

func (x *GetTripRodReq) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[42]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTripRodReq.ProtoReflect.Descriptor instead.
func (*GetTripRodReq) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{42}
}

func (x *GetTripRodReq) GetStorage() common.STORAGE_TYPE {
	if x != nil {
		return x.Storage
	}
	return common.STORAGE_TYPE(0)
}

// 获取钓组信息
type GetTripRodRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret  *common.Result       `protobuf:"bytes,1,opt,name=ret,proto3" json:"ret,omitempty"`   // 结果
	List []*common.RodBagInfo `protobuf:"bytes,2,rep,name=list,proto3" json:"list,omitempty"` // 钓组信息列表
}

func (x *GetTripRodRsp) Reset() {
	*x = GetTripRodRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[43]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTripRodRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTripRodRsp) ProtoMessage() {}

func (x *GetTripRodRsp) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[43]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTripRodRsp.ProtoReflect.Descriptor instead.
func (*GetTripRodRsp) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{43}
}

func (x *GetTripRodRsp) GetRet() *common.Result {
	if x != nil {
		return x.Ret
	}
	return nil
}

func (x *GetTripRodRsp) GetList() []*common.RodBagInfo {
	if x != nil {
		return x.List
	}
	return nil
}

// 导入预设钓组
type LoadTripRodReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RigId int32 `protobuf:"varint,2,opt,name=rig_id,json=rigId,proto3" json:"rig_id,omitempty"` // 鱼竿架id
	Id    int32 `protobuf:"varint,3,opt,name=id,proto3" json:"id,omitempty"`                    // 钓组id
}

func (x *LoadTripRodReq) Reset() {
	*x = LoadTripRodReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[44]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LoadTripRodReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoadTripRodReq) ProtoMessage() {}

func (x *LoadTripRodReq) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[44]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoadTripRodReq.ProtoReflect.Descriptor instead.
func (*LoadTripRodReq) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{44}
}

func (x *LoadTripRodReq) GetRigId() int32 {
	if x != nil {
		return x.RigId
	}
	return 0
}

func (x *LoadTripRodReq) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

// 导入预设钓组
type LoadTripRodRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret   *common.Result     `protobuf:"bytes,1,opt,name=ret,proto3" json:"ret,omitempty"`                   // 结果
	RigId int32              `protobuf:"varint,2,opt,name=rig_id,json=rigId,proto3" json:"rig_id,omitempty"` // 鱼竿架id
	Id    int32              `protobuf:"varint,3,opt,name=id,proto3" json:"id,omitempty"`                    // 钓组id
	Info  *common.RodBagInfo `protobuf:"bytes,4,opt,name=info,proto3" json:"info,omitempty"`                 // 钓组信息
}

func (x *LoadTripRodRsp) Reset() {
	*x = LoadTripRodRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[45]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LoadTripRodRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoadTripRodRsp) ProtoMessage() {}

func (x *LoadTripRodRsp) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[45]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoadTripRodRsp.ProtoReflect.Descriptor instead.
func (*LoadTripRodRsp) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{45}
}

func (x *LoadTripRodRsp) GetRet() *common.Result {
	if x != nil {
		return x.Ret
	}
	return nil
}

func (x *LoadTripRodRsp) GetRigId() int32 {
	if x != nil {
		return x.RigId
	}
	return 0
}

func (x *LoadTripRodRsp) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *LoadTripRodRsp) GetInfo() *common.RodBagInfo {
	if x != nil {
		return x.Info
	}
	return nil
}

// 保存预设钓组
type SaveTripRodReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RigId int32 `protobuf:"varint,2,opt,name=rig_id,json=rigId,proto3" json:"rig_id,omitempty"` // 鱼竿方案id
}

func (x *SaveTripRodReq) Reset() {
	*x = SaveTripRodReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[46]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SaveTripRodReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SaveTripRodReq) ProtoMessage() {}

func (x *SaveTripRodReq) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[46]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SaveTripRodReq.ProtoReflect.Descriptor instead.
func (*SaveTripRodReq) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{46}
}

func (x *SaveTripRodReq) GetRigId() int32 {
	if x != nil {
		return x.RigId
	}
	return 0
}

// 保存预设钓组
type SaveTripRodRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret   *common.Result     `protobuf:"bytes,1,opt,name=ret,proto3" json:"ret,omitempty"`                   // 结果
	RigId int32              `protobuf:"varint,2,opt,name=rig_id,json=rigId,proto3" json:"rig_id,omitempty"` // 鱼竿方案id
	Id    int32              `protobuf:"varint,3,opt,name=id,proto3" json:"id,omitempty"`                    // 杆组id
	Info  *common.RodBagInfo `protobuf:"bytes,4,opt,name=info,proto3" json:"info,omitempty"`                 // 钓组信息
}

func (x *SaveTripRodRsp) Reset() {
	*x = SaveTripRodRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[47]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SaveTripRodRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SaveTripRodRsp) ProtoMessage() {}

func (x *SaveTripRodRsp) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[47]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SaveTripRodRsp.ProtoReflect.Descriptor instead.
func (*SaveTripRodRsp) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{47}
}

func (x *SaveTripRodRsp) GetRet() *common.Result {
	if x != nil {
		return x.Ret
	}
	return nil
}

func (x *SaveTripRodRsp) GetRigId() int32 {
	if x != nil {
		return x.RigId
	}
	return 0
}

func (x *SaveTripRodRsp) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *SaveTripRodRsp) GetInfo() *common.RodBagInfo {
	if x != nil {
		return x.Info
	}
	return nil
}

// 保存新钓组
type SaveNewTripRodReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name     string              `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`                          // 钓组名称
	List     []*UpdateTripRodReq `protobuf:"bytes,2,rep,name=list,proto3" json:"list,omitempty"`                          // 修改的钓组信息
	BagIndex int32               `protobuf:"varint,3,opt,name=bag_index,json=bagIndex,proto3" json:"bag_index,omitempty"` // 背包位置
}

func (x *SaveNewTripRodReq) Reset() {
	*x = SaveNewTripRodReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[48]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SaveNewTripRodReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SaveNewTripRodReq) ProtoMessage() {}

func (x *SaveNewTripRodReq) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[48]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SaveNewTripRodReq.ProtoReflect.Descriptor instead.
func (*SaveNewTripRodReq) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{48}
}

func (x *SaveNewTripRodReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SaveNewTripRodReq) GetList() []*UpdateTripRodReq {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *SaveNewTripRodReq) GetBagIndex() int32 {
	if x != nil {
		return x.BagIndex
	}
	return 0
}

// 保存新钓组
type SaveNewTripRodRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret      *common.Result     `protobuf:"bytes,1,opt,name=ret,proto3" json:"ret,omitempty"` // 结果
	Id       int32              `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"`  // 杆组id
	Info     *common.RodBagInfo `protobuf:"bytes,3,opt,name=info,proto3" json:"info,omitempty"`
	BagIndex int32              `protobuf:"varint,4,opt,name=bag_index,json=bagIndex,proto3" json:"bag_index,omitempty"` // 背包位置
}

func (x *SaveNewTripRodRsp) Reset() {
	*x = SaveNewTripRodRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[49]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SaveNewTripRodRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SaveNewTripRodRsp) ProtoMessage() {}

func (x *SaveNewTripRodRsp) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[49]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SaveNewTripRodRsp.ProtoReflect.Descriptor instead.
func (*SaveNewTripRodRsp) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{49}
}

func (x *SaveNewTripRodRsp) GetRet() *common.Result {
	if x != nil {
		return x.Ret
	}
	return nil
}

func (x *SaveNewTripRodRsp) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *SaveNewTripRodRsp) GetInfo() *common.RodBagInfo {
	if x != nil {
		return x.Info
	}
	return nil
}

func (x *SaveNewTripRodRsp) GetBagIndex() int32 {
	if x != nil {
		return x.BagIndex
	}
	return 0
}

// 钓组放入背包请求
type PutTripRodReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id       int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`                             // 钓组id
	BagIndex int32 `protobuf:"varint,2,opt,name=bag_index,json=bagIndex,proto3" json:"bag_index,omitempty"` // 背包位置
}

func (x *PutTripRodReq) Reset() {
	*x = PutTripRodReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[50]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PutTripRodReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PutTripRodReq) ProtoMessage() {}

func (x *PutTripRodReq) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[50]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PutTripRodReq.ProtoReflect.Descriptor instead.
func (*PutTripRodReq) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{50}
}

func (x *PutTripRodReq) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *PutTripRodReq) GetBagIndex() int32 {
	if x != nil {
		return x.BagIndex
	}
	return 0
}

// 钓组放入背包返回
type PutTripRodRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret      *common.Result     `protobuf:"bytes,1,opt,name=ret,proto3" json:"ret,omitempty"`                            // 结果
	BagIndex int32              `protobuf:"varint,2,opt,name=bag_index,json=bagIndex,proto3" json:"bag_index,omitempty"` // 背包位置
	Id       int32              `protobuf:"varint,3,opt,name=id,proto3" json:"id,omitempty"`                             // 钓组id
	Info     *common.RodBagInfo `protobuf:"bytes,4,opt,name=info,proto3" json:"info,omitempty"`                          // 钓组信息
}

func (x *PutTripRodRsp) Reset() {
	*x = PutTripRodRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[51]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PutTripRodRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PutTripRodRsp) ProtoMessage() {}

func (x *PutTripRodRsp) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[51]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PutTripRodRsp.ProtoReflect.Descriptor instead.
func (*PutTripRodRsp) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{51}
}

func (x *PutTripRodRsp) GetRet() *common.Result {
	if x != nil {
		return x.Ret
	}
	return nil
}

func (x *PutTripRodRsp) GetBagIndex() int32 {
	if x != nil {
		return x.BagIndex
	}
	return 0
}

func (x *PutTripRodRsp) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *PutTripRodRsp) GetInfo() *common.RodBagInfo {
	if x != nil {
		return x.Info
	}
	return nil
}

// 卸下钓组
type DelTripRodReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"` // 钓组id
}

func (x *DelTripRodReq) Reset() {
	*x = DelTripRodReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[52]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DelTripRodReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DelTripRodReq) ProtoMessage() {}

func (x *DelTripRodReq) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[52]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DelTripRodReq.ProtoReflect.Descriptor instead.
func (*DelTripRodReq) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{52}
}

func (x *DelTripRodReq) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

// 卸下钓组
type DelTripRodRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret  *common.Result     `protobuf:"bytes,1,opt,name=ret,proto3" json:"ret,omitempty"`   // 结果
	Id   int32              `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"`    // 钓组id
	Info *common.RodBagInfo `protobuf:"bytes,3,opt,name=info,proto3" json:"info,omitempty"` // 钓组信息
}

func (x *DelTripRodRsp) Reset() {
	*x = DelTripRodRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[53]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DelTripRodRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DelTripRodRsp) ProtoMessage() {}

func (x *DelTripRodRsp) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[53]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DelTripRodRsp.ProtoReflect.Descriptor instead.
func (*DelTripRodRsp) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{53}
}

func (x *DelTripRodRsp) GetRet() *common.Result {
	if x != nil {
		return x.Ret
	}
	return nil
}

func (x *DelTripRodRsp) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *DelTripRodRsp) GetInfo() *common.RodBagInfo {
	if x != nil {
		return x.Info
	}
	return nil
}

// 拆卸钓组
type SplitTripRodReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"` // 钓组id
}

func (x *SplitTripRodReq) Reset() {
	*x = SplitTripRodReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[54]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SplitTripRodReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SplitTripRodReq) ProtoMessage() {}

func (x *SplitTripRodReq) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[54]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SplitTripRodReq.ProtoReflect.Descriptor instead.
func (*SplitTripRodReq) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{54}
}

func (x *SplitTripRodReq) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

// 拆卸钓组
type SplitTripRodRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret *common.Result `protobuf:"bytes,1,opt,name=ret,proto3" json:"ret,omitempty"` // 结果
	Id  int32          `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"`  // 钓组id
}

func (x *SplitTripRodRsp) Reset() {
	*x = SplitTripRodRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[55]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SplitTripRodRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SplitTripRodRsp) ProtoMessage() {}

func (x *SplitTripRodRsp) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[55]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SplitTripRodRsp.ProtoReflect.Descriptor instead.
func (*SplitTripRodRsp) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{55}
}

func (x *SplitTripRodRsp) GetRet() *common.Result {
	if x != nil {
		return x.Ret
	}
	return nil
}

func (x *SplitTripRodRsp) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

// 更新钓组 (上膛/ 改线/ 改饵)
type UpdateTripRodReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id         int32               `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`                                    // 钓组id
	Sit        int32               `protobuf:"varint,2,opt,name=sit,proto3" json:"sit,omitempty"`                                  // 钓组内位置信息
	ItemId     int64               `protobuf:"varint,3,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`              // 修改道具
	InstanceId string              `protobuf:"bytes,4,opt,name=instance_id,json=instanceId,proto3" json:"instance_id,omitempty"`   // 实例id
	Storage    common.STORAGE_TYPE `protobuf:"varint,5,opt,name=storage,proto3,enum=common.STORAGE_TYPE" json:"storage,omitempty"` // 存储类型
}

func (x *UpdateTripRodReq) Reset() {
	*x = UpdateTripRodReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[56]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateTripRodReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateTripRodReq) ProtoMessage() {}

func (x *UpdateTripRodReq) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[56]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateTripRodReq.ProtoReflect.Descriptor instead.
func (*UpdateTripRodReq) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{56}
}

func (x *UpdateTripRodReq) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateTripRodReq) GetSit() int32 {
	if x != nil {
		return x.Sit
	}
	return 0
}

func (x *UpdateTripRodReq) GetItemId() int64 {
	if x != nil {
		return x.ItemId
	}
	return 0
}

func (x *UpdateTripRodReq) GetInstanceId() string {
	if x != nil {
		return x.InstanceId
	}
	return ""
}

func (x *UpdateTripRodReq) GetStorage() common.STORAGE_TYPE {
	if x != nil {
		return x.Storage
	}
	return common.STORAGE_TYPE(0)
}

// 更新钓组 (上膛/ 改线/ 改饵)
type UpdateTripRodRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret        *common.Result     `protobuf:"bytes,1,opt,name=ret,proto3" json:"ret,omitempty"`                                 // 结果
	Id         int32              `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"`                                  // 钓组id
	Sit        int32              `protobuf:"varint,3,opt,name=sit,proto3" json:"sit,omitempty"`                                // 钓组内位置信息
	ItemId     int64              `protobuf:"varint,4,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`            // 修改道具
	InstanceId string             `protobuf:"bytes,5,opt,name=instance_id,json=instanceId,proto3" json:"instance_id,omitempty"` // 实例id
	Info       *common.RodBagInfo `protobuf:"bytes,6,opt,name=info,proto3" json:"info,omitempty"`
}

func (x *UpdateTripRodRsp) Reset() {
	*x = UpdateTripRodRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[57]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateTripRodRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateTripRodRsp) ProtoMessage() {}

func (x *UpdateTripRodRsp) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[57]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateTripRodRsp.ProtoReflect.Descriptor instead.
func (*UpdateTripRodRsp) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{57}
}

func (x *UpdateTripRodRsp) GetRet() *common.Result {
	if x != nil {
		return x.Ret
	}
	return nil
}

func (x *UpdateTripRodRsp) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateTripRodRsp) GetSit() int32 {
	if x != nil {
		return x.Sit
	}
	return 0
}

func (x *UpdateTripRodRsp) GetItemId() int64 {
	if x != nil {
		return x.ItemId
	}
	return 0
}

func (x *UpdateTripRodRsp) GetInstanceId() string {
	if x != nil {
		return x.InstanceId
	}
	return ""
}

func (x *UpdateTripRodRsp) GetInfo() *common.RodBagInfo {
	if x != nil {
		return x.Info
	}
	return nil
}

// 批量更新钓组
type BatchUpdateTripRodReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id   int32               `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`    // 钓组id
	List []*UpdateTripRodReq `protobuf:"bytes,2,rep,name=list,proto3" json:"list,omitempty"` // 修改的钓组信息
	Name string              `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"` // 钓组名称
}

func (x *BatchUpdateTripRodReq) Reset() {
	*x = BatchUpdateTripRodReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[58]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchUpdateTripRodReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchUpdateTripRodReq) ProtoMessage() {}

func (x *BatchUpdateTripRodReq) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[58]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchUpdateTripRodReq.ProtoReflect.Descriptor instead.
func (*BatchUpdateTripRodReq) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{58}
}

func (x *BatchUpdateTripRodReq) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *BatchUpdateTripRodReq) GetList() []*UpdateTripRodReq {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *BatchUpdateTripRodReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

// 批量更新钓组
type BatchUpdateTripRodRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret  *common.Result     `protobuf:"bytes,1,opt,name=ret,proto3" json:"ret,omitempty"` // 结果
	Info *common.RodBagInfo `protobuf:"bytes,6,opt,name=info,proto3" json:"info,omitempty"`
}

func (x *BatchUpdateTripRodRsp) Reset() {
	*x = BatchUpdateTripRodRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[59]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchUpdateTripRodRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchUpdateTripRodRsp) ProtoMessage() {}

func (x *BatchUpdateTripRodRsp) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[59]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchUpdateTripRodRsp.ProtoReflect.Descriptor instead.
func (*BatchUpdateTripRodRsp) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{59}
}

func (x *BatchUpdateTripRodRsp) GetRet() *common.Result {
	if x != nil {
		return x.Ret
	}
	return nil
}

func (x *BatchUpdateTripRodRsp) GetInfo() *common.RodBagInfo {
	if x != nil {
		return x.Info
	}
	return nil
}

// 获取背包请求
type GetTripBagReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type common.TRIP_BAG_TYPE `protobuf:"varint,2,opt,name=type,proto3,enum=common.TRIP_BAG_TYPE" json:"type,omitempty"` // 背包类型
}

func (x *GetTripBagReq) Reset() {
	*x = GetTripBagReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[60]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTripBagReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTripBagReq) ProtoMessage() {}

func (x *GetTripBagReq) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[60]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTripBagReq.ProtoReflect.Descriptor instead.
func (*GetTripBagReq) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{60}
}

func (x *GetTripBagReq) GetType() common.TRIP_BAG_TYPE {
	if x != nil {
		return x.Type
	}
	return common.TRIP_BAG_TYPE(0)
}

// 获取背包响应
type GetTripBagRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret  *common.Result       `protobuf:"bytes,1,opt,name=ret,proto3" json:"ret,omitempty"`                              // 结果
	Type common.TRIP_BAG_TYPE `protobuf:"varint,2,opt,name=type,proto3,enum=common.TRIP_BAG_TYPE" json:"type,omitempty"` // 背包类型
	List []*common.ItemInfo   `protobuf:"bytes,3,rep,name=list,proto3" json:"list,omitempty"`                            // 道具列表
}

func (x *GetTripBagRsp) Reset() {
	*x = GetTripBagRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[61]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTripBagRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTripBagRsp) ProtoMessage() {}

func (x *GetTripBagRsp) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[61]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTripBagRsp.ProtoReflect.Descriptor instead.
func (*GetTripBagRsp) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{61}
}

func (x *GetTripBagRsp) GetRet() *common.Result {
	if x != nil {
		return x.Ret
	}
	return nil
}

func (x *GetTripBagRsp) GetType() common.TRIP_BAG_TYPE {
	if x != nil {
		return x.Type
	}
	return common.TRIP_BAG_TYPE(0)
}

func (x *GetTripBagRsp) GetList() []*common.ItemInfo {
	if x != nil {
		return x.List
	}
	return nil
}

// 修改旅途背包请求
type ModifyTripBagReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type       common.TRIP_BAG_TYPE    `protobuf:"varint,1,opt,name=type,proto3,enum=common.TRIP_BAG_TYPE" json:"type,omitempty"`          // 背包类型
	Operate    common.TRIP_BAG_OPERATE `protobuf:"varint,2,opt,name=operate,proto3,enum=common.TRIP_BAG_OPERATE" json:"operate,omitempty"` // 操作类型
	ItemId     int64                   `protobuf:"varint,3,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`                  // 道具id
	InstanceId string                  `protobuf:"bytes,4,opt,name=instance_id,json=instanceId,proto3" json:"instance_id,omitempty"`       // 实例id
	Count      int64                   `protobuf:"varint,5,opt,name=count,proto3" json:"count,omitempty"`                                  // 数量
}

func (x *ModifyTripBagReq) Reset() {
	*x = ModifyTripBagReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[62]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModifyTripBagReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModifyTripBagReq) ProtoMessage() {}

func (x *ModifyTripBagReq) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[62]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModifyTripBagReq.ProtoReflect.Descriptor instead.
func (*ModifyTripBagReq) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{62}
}

func (x *ModifyTripBagReq) GetType() common.TRIP_BAG_TYPE {
	if x != nil {
		return x.Type
	}
	return common.TRIP_BAG_TYPE(0)
}

func (x *ModifyTripBagReq) GetOperate() common.TRIP_BAG_OPERATE {
	if x != nil {
		return x.Operate
	}
	return common.TRIP_BAG_OPERATE(0)
}

func (x *ModifyTripBagReq) GetItemId() int64 {
	if x != nil {
		return x.ItemId
	}
	return 0
}

func (x *ModifyTripBagReq) GetInstanceId() string {
	if x != nil {
		return x.InstanceId
	}
	return ""
}

func (x *ModifyTripBagReq) GetCount() int64 {
	if x != nil {
		return x.Count
	}
	return 0
}

// 修改旅途背包响应
type ModifyTripBagRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret     *common.Result          `protobuf:"bytes,1,opt,name=ret,proto3" json:"ret,omitempty"`                                       // 结果
	Type    common.TRIP_BAG_TYPE    `protobuf:"varint,2,opt,name=type,proto3,enum=common.TRIP_BAG_TYPE" json:"type,omitempty"`          // 背包类型
	Operate common.TRIP_BAG_OPERATE `protobuf:"varint,3,opt,name=operate,proto3,enum=common.TRIP_BAG_OPERATE" json:"operate,omitempty"` // 操作类型
	ItemId  int64                   `protobuf:"varint,4,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`                  // 道具id
}

func (x *ModifyTripBagRsp) Reset() {
	*x = ModifyTripBagRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[63]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModifyTripBagRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModifyTripBagRsp) ProtoMessage() {}

func (x *ModifyTripBagRsp) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[63]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModifyTripBagRsp.ProtoReflect.Descriptor instead.
func (*ModifyTripBagRsp) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{63}
}

func (x *ModifyTripBagRsp) GetRet() *common.Result {
	if x != nil {
		return x.Ret
	}
	return nil
}

func (x *ModifyTripBagRsp) GetType() common.TRIP_BAG_TYPE {
	if x != nil {
		return x.Type
	}
	return common.TRIP_BAG_TYPE(0)
}

func (x *ModifyTripBagRsp) GetOperate() common.TRIP_BAG_OPERATE {
	if x != nil {
		return x.Operate
	}
	return common.TRIP_BAG_OPERATE(0)
}

func (x *ModifyTripBagRsp) GetItemId() int64 {
	if x != nil {
		return x.ItemId
	}
	return 0
}

// 旅途背包快捷购买
type TripBagQuickBuyReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ItemId int64 `protobuf:"varint,3,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"` // 道具id
	Count  int64 `protobuf:"varint,4,opt,name=count,proto3" json:"count,omitempty"`                 // 数量
}

func (x *TripBagQuickBuyReq) Reset() {
	*x = TripBagQuickBuyReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[64]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TripBagQuickBuyReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TripBagQuickBuyReq) ProtoMessage() {}

func (x *TripBagQuickBuyReq) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[64]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TripBagQuickBuyReq.ProtoReflect.Descriptor instead.
func (*TripBagQuickBuyReq) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{64}
}

func (x *TripBagQuickBuyReq) GetItemId() int64 {
	if x != nil {
		return x.ItemId
	}
	return 0
}

func (x *TripBagQuickBuyReq) GetCount() int64 {
	if x != nil {
		return x.Count
	}
	return 0
}

// 旅途背包快捷购买
type TripBagQuickBuyRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret    *common.Result `protobuf:"bytes,1,opt,name=ret,proto3" json:"ret,omitempty"`                      // 结果
	ItemId int64          `protobuf:"varint,3,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"` // 道具id
	Count  int64          `protobuf:"varint,4,opt,name=count,proto3" json:"count,omitempty"`                 // 数量
}

func (x *TripBagQuickBuyRsp) Reset() {
	*x = TripBagQuickBuyRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[65]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TripBagQuickBuyRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TripBagQuickBuyRsp) ProtoMessage() {}

func (x *TripBagQuickBuyRsp) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[65]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TripBagQuickBuyRsp.ProtoReflect.Descriptor instead.
func (*TripBagQuickBuyRsp) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{65}
}

func (x *TripBagQuickBuyRsp) GetRet() *common.Result {
	if x != nil {
		return x.Ret
	}
	return nil
}

func (x *TripBagQuickBuyRsp) GetItemId() int64 {
	if x != nil {
		return x.ItemId
	}
	return 0
}

func (x *TripBagQuickBuyRsp) GetCount() int64 {
	if x != nil {
		return x.Count
	}
	return 0
}

// 旅途背包使用
type TripBagUseReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type   common.TRIP_BAG_TYPE `protobuf:"varint,2,opt,name=type,proto3,enum=common.TRIP_BAG_TYPE" json:"type,omitempty"` // 背包类型
	ItemId int64                `protobuf:"varint,3,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`         // 道具id
	Count  int64                `protobuf:"varint,4,opt,name=count,proto3" json:"count,omitempty"`                         // 数量
}

func (x *TripBagUseReq) Reset() {
	*x = TripBagUseReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[66]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TripBagUseReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TripBagUseReq) ProtoMessage() {}

func (x *TripBagUseReq) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[66]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TripBagUseReq.ProtoReflect.Descriptor instead.
func (*TripBagUseReq) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{66}
}

func (x *TripBagUseReq) GetType() common.TRIP_BAG_TYPE {
	if x != nil {
		return x.Type
	}
	return common.TRIP_BAG_TYPE(0)
}

func (x *TripBagUseReq) GetItemId() int64 {
	if x != nil {
		return x.ItemId
	}
	return 0
}

func (x *TripBagUseReq) GetCount() int64 {
	if x != nil {
		return x.Count
	}
	return 0
}

// 旅途背包使用
type TripBagUseRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret    *common.Result       `protobuf:"bytes,1,opt,name=ret,proto3" json:"ret,omitempty"`                              // 结果
	Type   common.TRIP_BAG_TYPE `protobuf:"varint,2,opt,name=type,proto3,enum=common.TRIP_BAG_TYPE" json:"type,omitempty"` // 背包类型
	ItemId int64                `protobuf:"varint,3,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`         // 道具id
	Count  int64                `protobuf:"varint,4,opt,name=count,proto3" json:"count,omitempty"`                         // 数量
}

func (x *TripBagUseRsp) Reset() {
	*x = TripBagUseRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[67]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TripBagUseRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TripBagUseRsp) ProtoMessage() {}

func (x *TripBagUseRsp) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[67]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TripBagUseRsp.ProtoReflect.Descriptor instead.
func (*TripBagUseRsp) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{67}
}

func (x *TripBagUseRsp) GetRet() *common.Result {
	if x != nil {
		return x.Ret
	}
	return nil
}

func (x *TripBagUseRsp) GetType() common.TRIP_BAG_TYPE {
	if x != nil {
		return x.Type
	}
	return common.TRIP_BAG_TYPE(0)
}

func (x *TripBagUseRsp) GetItemId() int64 {
	if x != nil {
		return x.ItemId
	}
	return 0
}

func (x *TripBagUseRsp) GetCount() int64 {
	if x != nil {
		return x.Count
	}
	return 0
}

// CheckForbidWordReq 检查屏蔽字请求
type CheckForbidWordReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Word string `protobuf:"bytes,1,opt,name=word,proto3" json:"word,omitempty"` // 需要检测的单词或内容
}

func (x *CheckForbidWordReq) Reset() {
	*x = CheckForbidWordReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[68]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckForbidWordReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckForbidWordReq) ProtoMessage() {}

func (x *CheckForbidWordReq) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[68]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckForbidWordReq.ProtoReflect.Descriptor instead.
func (*CheckForbidWordReq) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{68}
}

func (x *CheckForbidWordReq) GetWord() string {
	if x != nil {
		return x.Word
	}
	return ""
}

// CheckForbidWordRsp 检查屏蔽字响应
type CheckForbidWordRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IsForbid   bool   `protobuf:"varint,1,opt,name=is_forbid,json=isForbid,proto3" json:"is_forbid,omitempty"`      // 是否含有屏蔽字
	ForbidWord string `protobuf:"bytes,2,opt,name=forbid_word,json=forbidWord,proto3" json:"forbid_word,omitempty"` // 处理后的单词或内容
}

func (x *CheckForbidWordRsp) Reset() {
	*x = CheckForbidWordRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[69]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckForbidWordRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckForbidWordRsp) ProtoMessage() {}

func (x *CheckForbidWordRsp) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[69]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckForbidWordRsp.ProtoReflect.Descriptor instead.
func (*CheckForbidWordRsp) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{69}
}

func (x *CheckForbidWordRsp) GetIsForbid() bool {
	if x != nil {
		return x.IsForbid
	}
	return false
}

func (x *CheckForbidWordRsp) GetForbidWord() string {
	if x != nil {
		return x.ForbidWord
	}
	return ""
}

// 卸下旅行背包所有物品请求
type UnloadAllTripBagReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UnloadAllTripBagReq) Reset() {
	*x = UnloadAllTripBagReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[70]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UnloadAllTripBagReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UnloadAllTripBagReq) ProtoMessage() {}

func (x *UnloadAllTripBagReq) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[70]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UnloadAllTripBagReq.ProtoReflect.Descriptor instead.
func (*UnloadAllTripBagReq) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{70}
}

// 卸下旅行背包所有物品返回
type UnloadAllTripBagRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret *common.Result `protobuf:"bytes,1,opt,name=ret,proto3" json:"ret,omitempty"` // 结果
}

func (x *UnloadAllTripBagRsp) Reset() {
	*x = UnloadAllTripBagRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[71]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UnloadAllTripBagRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UnloadAllTripBagRsp) ProtoMessage() {}

func (x *UnloadAllTripBagRsp) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[71]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UnloadAllTripBagRsp.ProtoReflect.Descriptor instead.
func (*UnloadAllTripBagRsp) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{71}
}

func (x *UnloadAllTripBagRsp) GetRet() *common.Result {
	if x != nil {
		return x.Ret
	}
	return nil
}

// 拉取鱼饵数据
type ItemHeapReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ItemHeapReq) Reset() {
	*x = ItemHeapReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[72]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ItemHeapReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ItemHeapReq) ProtoMessage() {}

func (x *ItemHeapReq) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[72]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ItemHeapReq.ProtoReflect.Descriptor instead.
func (*ItemHeapReq) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{72}
}

// 拉取鱼饵数据
type ItemHeapRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret       *common.Result  `protobuf:"bytes,1,opt,name=ret,proto3" json:"ret,omitempty"`                                                                                                                        // 结果
	ItemHeaps map[int64]int32 `protobuf:"bytes,2,rep,name=item_heaps,json=itemHeaps,proto3" json:"item_heaps,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"` // 鱼饵数据 key: 物品id value: 耐久损耗度百分比
}

func (x *ItemHeapRsp) Reset() {
	*x = ItemHeapRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[73]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ItemHeapRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ItemHeapRsp) ProtoMessage() {}

func (x *ItemHeapRsp) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[73]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ItemHeapRsp.ProtoReflect.Descriptor instead.
func (*ItemHeapRsp) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{73}
}

func (x *ItemHeapRsp) GetRet() *common.Result {
	if x != nil {
		return x.Ret
	}
	return nil
}

func (x *ItemHeapRsp) GetItemHeaps() map[int64]int32 {
	if x != nil {
		return x.ItemHeaps
	}
	return nil
}

// 鱼饵数据更新
type ItemHeapUpdateNotify struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ItemHeaps map[int64]int32 `protobuf:"bytes,1,rep,name=item_heaps,json=itemHeaps,proto3" json:"item_heaps,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"` // 鱼饵数据 key: 物品id value: 耐久损耗都百分比
}

func (x *ItemHeapUpdateNotify) Reset() {
	*x = ItemHeapUpdateNotify{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[74]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ItemHeapUpdateNotify) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ItemHeapUpdateNotify) ProtoMessage() {}

func (x *ItemHeapUpdateNotify) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[74]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ItemHeapUpdateNotify.ProtoReflect.Descriptor instead.
func (*ItemHeapUpdateNotify) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{74}
}

func (x *ItemHeapUpdateNotify) GetItemHeaps() map[int64]int32 {
	if x != nil {
		return x.ItemHeaps
	}
	return nil
}

// RealNameAuthReq 实名认证请求
type RealNameAuthReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RealName  string `protobuf:"bytes,1,opt,name=real_name,json=realName,proto3" json:"real_name,omitempty"`      // 真实姓名
	IdCardNum string `protobuf:"bytes,2,opt,name=id_card_num,json=idCardNum,proto3" json:"id_card_num,omitempty"` // 身份证号
}

func (x *RealNameAuthReq) Reset() {
	*x = RealNameAuthReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[75]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RealNameAuthReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RealNameAuthReq) ProtoMessage() {}

func (x *RealNameAuthReq) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[75]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RealNameAuthReq.ProtoReflect.Descriptor instead.
func (*RealNameAuthReq) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{75}
}

func (x *RealNameAuthReq) GetRealName() string {
	if x != nil {
		return x.RealName
	}
	return ""
}

func (x *RealNameAuthReq) GetIdCardNum() string {
	if x != nil {
		return x.IdCardNum
	}
	return ""
}

// RealNameAuthRsp 实名认证响应
type RealNameAuthRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret *common.Result `protobuf:"bytes,1,opt,name=ret,proto3" json:"ret,omitempty"` // 结果
}

func (x *RealNameAuthRsp) Reset() {
	*x = RealNameAuthRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[76]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RealNameAuthRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RealNameAuthRsp) ProtoMessage() {}

func (x *RealNameAuthRsp) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[76]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RealNameAuthRsp.ProtoReflect.Descriptor instead.
func (*RealNameAuthRsp) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{76}
}

func (x *RealNameAuthRsp) GetRet() *common.Result {
	if x != nil {
		return x.Ret
	}
	return nil
}

// AntiAddictionNtf 防沉迷通知
type AntiAddictionNtf struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IsChild    bool  `protobuf:"varint,1,opt,name=is_child,json=isChild,proto3" json:"is_child,omitempty"`          // 是否未成年人
	ExceedTime int32 `protobuf:"varint,2,opt,name=exceed_time,json=exceedTime,proto3" json:"exceed_time,omitempty"` // 超过时间（秒）
}

func (x *AntiAddictionNtf) Reset() {
	*x = AntiAddictionNtf{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[77]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AntiAddictionNtf) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AntiAddictionNtf) ProtoMessage() {}

func (x *AntiAddictionNtf) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[77]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AntiAddictionNtf.ProtoReflect.Descriptor instead.
func (*AntiAddictionNtf) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{77}
}

func (x *AntiAddictionNtf) GetIsChild() bool {
	if x != nil {
		return x.IsChild
	}
	return false
}

func (x *AntiAddictionNtf) GetExceedTime() int32 {
	if x != nil {
		return x.ExceedTime
	}
	return 0
}

// TempCashBuyReq 临时现金购买请求(针对版号版本)
type TempCashBuyReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StoreBuyId int64 `protobuf:"varint,1,opt,name=store_buy_id,json=storeBuyId,proto3" json:"store_buy_id,omitempty"` // 现金购买id
}

func (x *TempCashBuyReq) Reset() {
	*x = TempCashBuyReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[78]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TempCashBuyReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TempCashBuyReq) ProtoMessage() {}

func (x *TempCashBuyReq) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[78]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TempCashBuyReq.ProtoReflect.Descriptor instead.
func (*TempCashBuyReq) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{78}
}

func (x *TempCashBuyReq) GetStoreBuyId() int64 {
	if x != nil {
		return x.StoreBuyId
	}
	return 0
}

// TempCashBuyRsp 临时现金购买响应(针对版号版本)
type TempCashBuyRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret        *common.Result `protobuf:"bytes,1,opt,name=ret,proto3" json:"ret,omitempty"`                                    // 结果
	StoreBuyId int64          `protobuf:"varint,2,opt,name=store_buy_id,json=storeBuyId,proto3" json:"store_buy_id,omitempty"` // 现金购买id
	RewardInfo *common.Reward `protobuf:"bytes,3,opt,name=reward_info,json=rewardInfo,proto3" json:"reward_info,omitempty"`    // 奖励信息
}

func (x *TempCashBuyRsp) Reset() {
	*x = TempCashBuyRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[79]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TempCashBuyRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TempCashBuyRsp) ProtoMessage() {}

func (x *TempCashBuyRsp) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[79]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TempCashBuyRsp.ProtoReflect.Descriptor instead.
func (*TempCashBuyRsp) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{79}
}

func (x *TempCashBuyRsp) GetRet() *common.Result {
	if x != nil {
		return x.Ret
	}
	return nil
}

func (x *TempCashBuyRsp) GetStoreBuyId() int64 {
	if x != nil {
		return x.StoreBuyId
	}
	return 0
}

func (x *TempCashBuyRsp) GetRewardInfo() *common.Reward {
	if x != nil {
		return x.RewardInfo
	}
	return nil
}

// UpdateGuideProgressReq 新手引导进度更新请求
type UpdateGuideProgressReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Progress int32 `protobuf:"varint,1,opt,name=progress,proto3" json:"progress,omitempty"` // 进度
}

func (x *UpdateGuideProgressReq) Reset() {
	*x = UpdateGuideProgressReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[80]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateGuideProgressReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateGuideProgressReq) ProtoMessage() {}

func (x *UpdateGuideProgressReq) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[80]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateGuideProgressReq.ProtoReflect.Descriptor instead.
func (*UpdateGuideProgressReq) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{80}
}

func (x *UpdateGuideProgressReq) GetProgress() int32 {
	if x != nil {
		return x.Progress
	}
	return 0
}

// UpdateGuideProgressRsp 新手引导进度更新应答(如果有奖励使用通用奖励推送:UpdateItemInfoNtf)
type UpdateGuideProgressRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret      *common.Result `protobuf:"bytes,1,opt,name=ret,proto3" json:"ret,omitempty"`            // 结果
	Progress int32          `protobuf:"varint,2,opt,name=progress,proto3" json:"progress,omitempty"` // 进度
}

func (x *UpdateGuideProgressRsp) Reset() {
	*x = UpdateGuideProgressRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[81]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateGuideProgressRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateGuideProgressRsp) ProtoMessage() {}

func (x *UpdateGuideProgressRsp) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[81]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateGuideProgressRsp.ProtoReflect.Descriptor instead.
func (*UpdateGuideProgressRsp) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{81}
}

func (x *UpdateGuideProgressRsp) GetRet() *common.Result {
	if x != nil {
		return x.Ret
	}
	return nil
}

func (x *UpdateGuideProgressRsp) GetProgress() int32 {
	if x != nil {
		return x.Progress
	}
	return 0
}

// 连续登录查询
type ContinuousLoginReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ContinuousLoginReq) Reset() {
	*x = ContinuousLoginReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[82]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ContinuousLoginReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ContinuousLoginReq) ProtoMessage() {}

func (x *ContinuousLoginReq) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[82]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ContinuousLoginReq.ProtoReflect.Descriptor instead.
func (*ContinuousLoginReq) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{82}
}

type ContinuousLoginRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret      *common.Result `protobuf:"bytes,1,opt,name=ret,proto3" json:"ret,omitempty"`                            // 结果
	UpdateTs int64          `protobuf:"varint,2,opt,name=update_ts,json=updateTs,proto3" json:"update_ts,omitempty"` // 上一次更新时间
	Day      int32          `protobuf:"varint,3,opt,name=day,proto3" json:"day,omitempty"`                           // 已领奖日
}

func (x *ContinuousLoginRsp) Reset() {
	*x = ContinuousLoginRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[83]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ContinuousLoginRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ContinuousLoginRsp) ProtoMessage() {}

func (x *ContinuousLoginRsp) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[83]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ContinuousLoginRsp.ProtoReflect.Descriptor instead.
func (*ContinuousLoginRsp) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{83}
}

func (x *ContinuousLoginRsp) GetRet() *common.Result {
	if x != nil {
		return x.Ret
	}
	return nil
}

func (x *ContinuousLoginRsp) GetUpdateTs() int64 {
	if x != nil {
		return x.UpdateTs
	}
	return 0
}

func (x *ContinuousLoginRsp) GetDay() int32 {
	if x != nil {
		return x.Day
	}
	return 0
}

type ContinuousLoginRewardReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ContinuousLoginRewardReq) Reset() {
	*x = ContinuousLoginRewardReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[84]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ContinuousLoginRewardReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ContinuousLoginRewardReq) ProtoMessage() {}

func (x *ContinuousLoginRewardReq) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[84]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ContinuousLoginRewardReq.ProtoReflect.Descriptor instead.
func (*ContinuousLoginRewardReq) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{84}
}

type ContinuousLoginRewardRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret      *common.Result `protobuf:"bytes,1,opt,name=ret,proto3" json:"ret,omitempty"`                            // 结果
	UpdateTs int64          `protobuf:"varint,2,opt,name=update_ts,json=updateTs,proto3" json:"update_ts,omitempty"` // 更新时间
	Day      int32          `protobuf:"varint,3,opt,name=day,proto3" json:"day,omitempty"`                           // 已领奖日
	Reward   *common.Reward `protobuf:"bytes,5,opt,name=reward,proto3" json:"reward,omitempty"`                      // 奖励
}

func (x *ContinuousLoginRewardRsp) Reset() {
	*x = ContinuousLoginRewardRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[85]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ContinuousLoginRewardRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ContinuousLoginRewardRsp) ProtoMessage() {}

func (x *ContinuousLoginRewardRsp) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[85]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ContinuousLoginRewardRsp.ProtoReflect.Descriptor instead.
func (*ContinuousLoginRewardRsp) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{85}
}

func (x *ContinuousLoginRewardRsp) GetRet() *common.Result {
	if x != nil {
		return x.Ret
	}
	return nil
}

func (x *ContinuousLoginRewardRsp) GetUpdateTs() int64 {
	if x != nil {
		return x.UpdateTs
	}
	return 0
}

func (x *ContinuousLoginRewardRsp) GetDay() int32 {
	if x != nil {
		return x.Day
	}
	return 0
}

func (x *ContinuousLoginRewardRsp) GetReward() *common.Reward {
	if x != nil {
		return x.Reward
	}
	return nil
}

// 红点信息
type RedDotInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ModuleType    common.USER_MODULE_TYPE `protobuf:"varint,1,opt,name=module_type,json=moduleType,proto3,enum=common.USER_MODULE_TYPE" json:"module_type,omitempty"` // 主模块类型
	SubModuleType int32                   `protobuf:"varint,2,opt,name=sub_module_type,json=subModuleType,proto3" json:"sub_module_type,omitempty"`                   // 子模块类型，每个模块有不同的常量定义
	HasRedDot     bool                    `protobuf:"varint,3,opt,name=has_red_dot,json=hasRedDot,proto3" json:"has_red_dot,omitempty"`                               // 是否有红点
}

func (x *RedDotInfo) Reset() {
	*x = RedDotInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[86]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RedDotInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RedDotInfo) ProtoMessage() {}

func (x *RedDotInfo) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[86]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RedDotInfo.ProtoReflect.Descriptor instead.
func (*RedDotInfo) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{86}
}

func (x *RedDotInfo) GetModuleType() common.USER_MODULE_TYPE {
	if x != nil {
		return x.ModuleType
	}
	return common.USER_MODULE_TYPE(0)
}

func (x *RedDotInfo) GetSubModuleType() int32 {
	if x != nil {
		return x.SubModuleType
	}
	return 0
}

func (x *RedDotInfo) GetHasRedDot() bool {
	if x != nil {
		return x.HasRedDot
	}
	return false
}

// 清除红点请求
type ClearRedDotRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ModuleType    common.USER_MODULE_TYPE `protobuf:"varint,2,opt,name=module_type,json=moduleType,proto3,enum=common.USER_MODULE_TYPE" json:"module_type,omitempty"` // 主模块类型
	SubModuleType int32                   `protobuf:"varint,3,opt,name=sub_module_type,json=subModuleType,proto3" json:"sub_module_type,omitempty"`                   // 子模块类型
}

func (x *ClearRedDotRequest) Reset() {
	*x = ClearRedDotRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[87]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ClearRedDotRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClearRedDotRequest) ProtoMessage() {}

func (x *ClearRedDotRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[87]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClearRedDotRequest.ProtoReflect.Descriptor instead.
func (*ClearRedDotRequest) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{87}
}

func (x *ClearRedDotRequest) GetModuleType() common.USER_MODULE_TYPE {
	if x != nil {
		return x.ModuleType
	}
	return common.USER_MODULE_TYPE(0)
}

func (x *ClearRedDotRequest) GetSubModuleType() int32 {
	if x != nil {
		return x.SubModuleType
	}
	return 0
}

// 清除红点响应
type ClearRedDotResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret    *common.Result `protobuf:"bytes,1,opt,name=ret,proto3" json:"ret,omitempty"`                     // 结果
	RedDot *RedDotInfo    `protobuf:"bytes,2,opt,name=red_dot,json=redDot,proto3" json:"red_dot,omitempty"` // 当前红点状态
}

func (x *ClearRedDotResponse) Reset() {
	*x = ClearRedDotResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[88]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ClearRedDotResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClearRedDotResponse) ProtoMessage() {}

func (x *ClearRedDotResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[88]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClearRedDotResponse.ProtoReflect.Descriptor instead.
func (*ClearRedDotResponse) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{88}
}

func (x *ClearRedDotResponse) GetRet() *common.Result {
	if x != nil {
		return x.Ret
	}
	return nil
}

func (x *ClearRedDotResponse) GetRedDot() *RedDotInfo {
	if x != nil {
		return x.RedDot
	}
	return nil
}

// 获取玩家所有红点请求
type GetPlayerAllRedDotsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GetPlayerAllRedDotsRequest) Reset() {
	*x = GetPlayerAllRedDotsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[89]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPlayerAllRedDotsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPlayerAllRedDotsRequest) ProtoMessage() {}

func (x *GetPlayerAllRedDotsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[89]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPlayerAllRedDotsRequest.ProtoReflect.Descriptor instead.
func (*GetPlayerAllRedDotsRequest) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{89}
}

// 获取玩家所有红点响应
type GetPlayerAllRedDotsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret     *common.Result `protobuf:"bytes,1,opt,name=ret,proto3" json:"ret,omitempty"`                        // 结果
	RedDots []*RedDotInfo  `protobuf:"bytes,2,rep,name=red_dots,json=redDots,proto3" json:"red_dots,omitempty"` // 红点列表
}

func (x *GetPlayerAllRedDotsResponse) Reset() {
	*x = GetPlayerAllRedDotsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[90]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPlayerAllRedDotsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPlayerAllRedDotsResponse) ProtoMessage() {}

func (x *GetPlayerAllRedDotsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[90]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPlayerAllRedDotsResponse.ProtoReflect.Descriptor instead.
func (*GetPlayerAllRedDotsResponse) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{90}
}

func (x *GetPlayerAllRedDotsResponse) GetRet() *common.Result {
	if x != nil {
		return x.Ret
	}
	return nil
}

func (x *GetPlayerAllRedDotsResponse) GetRedDots() []*RedDotInfo {
	if x != nil {
		return x.RedDots
	}
	return nil
}

// 获取玩家指定模块红点请求
type GetPlayerModuleRedDotsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ModuleType common.USER_MODULE_TYPE `protobuf:"varint,1,opt,name=module_type,json=moduleType,proto3,enum=common.USER_MODULE_TYPE" json:"module_type,omitempty"` // 主模块类型
}

func (x *GetPlayerModuleRedDotsRequest) Reset() {
	*x = GetPlayerModuleRedDotsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[91]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPlayerModuleRedDotsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPlayerModuleRedDotsRequest) ProtoMessage() {}

func (x *GetPlayerModuleRedDotsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[91]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPlayerModuleRedDotsRequest.ProtoReflect.Descriptor instead.
func (*GetPlayerModuleRedDotsRequest) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{91}
}

func (x *GetPlayerModuleRedDotsRequest) GetModuleType() common.USER_MODULE_TYPE {
	if x != nil {
		return x.ModuleType
	}
	return common.USER_MODULE_TYPE(0)
}

// 获取玩家指定模块红点响应
type GetPlayerModuleRedDotsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret     *common.Result `protobuf:"bytes,1,opt,name=ret,proto3" json:"ret,omitempty"`                        // 结果
	RedDots []*RedDotInfo  `protobuf:"bytes,2,rep,name=red_dots,json=redDots,proto3" json:"red_dots,omitempty"` // 红点列表
}

func (x *GetPlayerModuleRedDotsResponse) Reset() {
	*x = GetPlayerModuleRedDotsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[92]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPlayerModuleRedDotsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPlayerModuleRedDotsResponse) ProtoMessage() {}

func (x *GetPlayerModuleRedDotsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[92]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPlayerModuleRedDotsResponse.ProtoReflect.Descriptor instead.
func (*GetPlayerModuleRedDotsResponse) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{92}
}

func (x *GetPlayerModuleRedDotsResponse) GetRet() *common.Result {
	if x != nil {
		return x.Ret
	}
	return nil
}

func (x *GetPlayerModuleRedDotsResponse) GetRedDots() []*RedDotInfo {
	if x != nil {
		return x.RedDots
	}
	return nil
}

// 更新玩家红点推送
type RedDotUpdateNotify struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RedDots []*RedDotInfo `protobuf:"bytes,1,rep,name=red_dots,json=redDots,proto3" json:"red_dots,omitempty"` // 红点列表
}

func (x *RedDotUpdateNotify) Reset() {
	*x = RedDotUpdateNotify{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[93]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RedDotUpdateNotify) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RedDotUpdateNotify) ProtoMessage() {}

func (x *RedDotUpdateNotify) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[93]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RedDotUpdateNotify.ProtoReflect.Descriptor instead.
func (*RedDotUpdateNotify) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{93}
}

func (x *RedDotUpdateNotify) GetRedDots() []*RedDotInfo {
	if x != nil {
		return x.RedDots
	}
	return nil
}

// 修改玩家信息请求
type ModifyPlayerInfoReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NickName string `protobuf:"bytes,1,opt,name=nick_name,json=nickName,proto3" json:"nick_name,omitempty"` // 昵称
	Avatar   int64  `protobuf:"varint,2,opt,name=avatar,proto3" json:"avatar,omitempty"`                    // 头像
	Frame    int64  `protobuf:"varint,3,opt,name=frame,proto3" json:"frame,omitempty"`                      // 头像框
}

func (x *ModifyPlayerInfoReq) Reset() {
	*x = ModifyPlayerInfoReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[94]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModifyPlayerInfoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModifyPlayerInfoReq) ProtoMessage() {}

func (x *ModifyPlayerInfoReq) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[94]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModifyPlayerInfoReq.ProtoReflect.Descriptor instead.
func (*ModifyPlayerInfoReq) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{94}
}

func (x *ModifyPlayerInfoReq) GetNickName() string {
	if x != nil {
		return x.NickName
	}
	return ""
}

func (x *ModifyPlayerInfoReq) GetAvatar() int64 {
	if x != nil {
		return x.Avatar
	}
	return 0
}

func (x *ModifyPlayerInfoReq) GetFrame() int64 {
	if x != nil {
		return x.Frame
	}
	return 0
}

// 修改玩家信息响应
type ModifyPlayerInfoRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret *common.Result `protobuf:"bytes,1,opt,name=ret,proto3" json:"ret,omitempty"` // 结果
}

func (x *ModifyPlayerInfoRsp) Reset() {
	*x = ModifyPlayerInfoRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[95]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModifyPlayerInfoRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModifyPlayerInfoRsp) ProtoMessage() {}

func (x *ModifyPlayerInfoRsp) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[95]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModifyPlayerInfoRsp.ProtoReflect.Descriptor instead.
func (*ModifyPlayerInfoRsp) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{95}
}

func (x *ModifyPlayerInfoRsp) GetRet() *common.Result {
	if x != nil {
		return x.Ret
	}
	return nil
}

// CDKey兑换码请求
type CDKeyExchangeReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CdKey string `protobuf:"bytes,1,opt,name=cd_key,json=cdKey,proto3" json:"cd_key,omitempty"` // CDKey兑换码
}

func (x *CDKeyExchangeReq) Reset() {
	*x = CDKeyExchangeReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[96]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CDKeyExchangeReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CDKeyExchangeReq) ProtoMessage() {}

func (x *CDKeyExchangeReq) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[96]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CDKeyExchangeReq.ProtoReflect.Descriptor instead.
func (*CDKeyExchangeReq) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{96}
}

func (x *CDKeyExchangeReq) GetCdKey() string {
	if x != nil {
		return x.CdKey
	}
	return ""
}

// CDKey兑换码响应
type CDKeyExchangeRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret        *common.Result `protobuf:"bytes,1,opt,name=ret,proto3" json:"ret,omitempty"`                                 // 结果
	RewardInfo *common.Reward `protobuf:"bytes,2,opt,name=reward_info,json=rewardInfo,proto3" json:"reward_info,omitempty"` // 奖励信息
}

func (x *CDKeyExchangeRsp) Reset() {
	*x = CDKeyExchangeRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_hall_proto_msgTypes[97]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CDKeyExchangeRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CDKeyExchangeRsp) ProtoMessage() {}

func (x *CDKeyExchangeRsp) ProtoReflect() protoreflect.Message {
	mi := &file_hall_proto_msgTypes[97]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CDKeyExchangeRsp.ProtoReflect.Descriptor instead.
func (*CDKeyExchangeRsp) Descriptor() ([]byte, []int) {
	return file_hall_proto_rawDescGZIP(), []int{97}
}

func (x *CDKeyExchangeRsp) GetRet() *common.Result {
	if x != nil {
		return x.Ret
	}
	return nil
}

func (x *CDKeyExchangeRsp) GetRewardInfo() *common.Reward {
	if x != nil {
		return x.RewardInfo
	}
	return nil
}

var File_hall_proto protoreflect.FileDescriptor

var file_hall_proto_rawDesc = []byte{
	0x0a, 0x0a, 0x68, 0x61, 0x6c, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x06, 0x68, 0x61,
	0x6c, 0x6c, 0x50, 0x42, 0x1a, 0x0a, 0x65, 0x6e, 0x75, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x0c, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x0c,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x10, 0x0a, 0x0e,
	0x47, 0x65, 0x74, 0x52, 0x6f, 0x6f, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x22, 0x61,
	0x0a, 0x0e, 0x47, 0x65, 0x74, 0x52, 0x6f, 0x6f, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x73, 0x70,
	0x12, 0x20, 0x0a, 0x03, 0x72, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x03, 0x72,
	0x65, 0x74, 0x12, 0x2d, 0x0a, 0x09, 0x72, 0x6f, 0x6f, 0x6d, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52,
	0x6f, 0x6f, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x08, 0x72, 0x6f, 0x6f, 0x6d, 0x49, 0x6e, 0x66,
	0x6f, 0x22, 0x14, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x4c, 0x61, 0x73, 0x74, 0x47, 0x61, 0x6d, 0x65,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x22, 0x65, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x4c, 0x61,
	0x73, 0x74, 0x47, 0x61, 0x6d, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x73, 0x70, 0x12, 0x20, 0x0a,
	0x03, 0x72, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x03, 0x72, 0x65, 0x74, 0x12,
	0x2d, 0x0a, 0x09, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x67, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x10, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x6f, 0x6f, 0x6d,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x08, 0x6c, 0x61, 0x73, 0x74, 0x47, 0x61, 0x6d, 0x65, 0x22, 0xa3,
	0x01, 0x0a, 0x0f, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x46, 0x69, 0x73, 0x68, 0x65, 0x72, 0x79, 0x52,
	0x65, 0x71, 0x12, 0x17, 0x0a, 0x07, 0x70, 0x6f, 0x6e, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x06, 0x70, 0x6f, 0x6e, 0x64, 0x49, 0x64, 0x12, 0x2e, 0x0a, 0x09, 0x67,
	0x61, 0x6d, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x11,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x47, 0x41, 0x4d, 0x45, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x52, 0x08, 0x67, 0x61, 0x6d, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2e, 0x0a, 0x09, 0x72,
	0x6f, 0x6f, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x11,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x4f, 0x4f, 0x4d, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x52, 0x08, 0x72, 0x6f, 0x6f, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x73,
	0x70, 0x6f, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x73, 0x70,
	0x6f, 0x74, 0x49, 0x64, 0x22, 0x94, 0x01, 0x0a, 0x0f, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x46, 0x69,
	0x73, 0x68, 0x65, 0x72, 0x79, 0x52, 0x73, 0x70, 0x12, 0x20, 0x0a, 0x03, 0x72, 0x65, 0x74, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x03, 0x72, 0x65, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x70, 0x6f,
	0x6e, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x70, 0x6f, 0x6e,
	0x64, 0x49, 0x64, 0x12, 0x2d, 0x0a, 0x09, 0x72, 0x6f, 0x6f, 0x6d, 0x5f, 0x69, 0x6e, 0x66, 0x6f,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x52, 0x6f, 0x6f, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x08, 0x72, 0x6f, 0x6f, 0x6d, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x17, 0x0a, 0x07, 0x73, 0x70, 0x6f, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x06, 0x73, 0x70, 0x6f, 0x74, 0x49, 0x64, 0x22, 0x2d, 0x0a, 0x0e, 0x47,
	0x65, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x12, 0x1b, 0x0a,
	0x09, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03,
	0x52, 0x08, 0x69, 0x74, 0x65, 0x6d, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x7e, 0x0a, 0x0e, 0x47, 0x65,
	0x74, 0x49, 0x74, 0x65, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x73, 0x70, 0x12, 0x20, 0x0a, 0x03,
	0x72, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x03, 0x72, 0x65, 0x74, 0x12, 0x1b,
	0x0a, 0x09, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x03, 0x52, 0x08, 0x69, 0x74, 0x65, 0x6d, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x2d, 0x0a, 0x09, 0x69,
	0x74, 0x65, 0x6d, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x10,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x49, 0x74, 0x65, 0x6d, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x08, 0x69, 0x74, 0x65, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x46, 0x0a, 0x14, 0x47, 0x65,
	0x74, 0x49, 0x74, 0x65, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x42, 0x79, 0x54, 0x79, 0x70, 0x65, 0x52,
	0x65, 0x71, 0x12, 0x2e, 0x0a, 0x09, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x11, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x49,
	0x54, 0x45, 0x4d, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x52, 0x08, 0x69, 0x74, 0x65, 0x6d, 0x54, 0x79,
	0x70, 0x65, 0x22, 0x67, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x49, 0x6e, 0x66,
	0x6f, 0x42, 0x79, 0x54, 0x79, 0x70, 0x65, 0x52, 0x73, 0x70, 0x12, 0x20, 0x0a, 0x03, 0x72, 0x65,
	0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x03, 0x72, 0x65, 0x74, 0x12, 0x2d, 0x0a, 0x09,
	0x69, 0x74, 0x65, 0x6d, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x10, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x49, 0x74, 0x65, 0x6d, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x08, 0x69, 0x74, 0x65, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x74, 0x0a, 0x11, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x4e, 0x74, 0x66,
	0x12, 0x2f, 0x0a, 0x0b, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x52, 0x0a, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x2e, 0x0a, 0x07, 0x73, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x14, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x53, 0x54, 0x4f, 0x52,
	0x41, 0x47, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x52, 0x07, 0x73, 0x74, 0x6f, 0x72, 0x61, 0x67,
	0x65, 0x22, 0x33, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x47, 0x6f, 0x6f, 0x64, 0x73, 0x42, 0x75, 0x79,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x12, 0x1d, 0x0a, 0x0a, 0x67, 0x6f, 0x6f, 0x64, 0x73,
	0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x52, 0x09, 0x67, 0x6f, 0x6f,
	0x64, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x67, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x47, 0x6f, 0x6f,
	0x64, 0x73, 0x42, 0x75, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x73, 0x70, 0x12, 0x20, 0x0a, 0x03,
	0x72, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x03, 0x72, 0x65, 0x74, 0x12, 0x2f,
	0x0a, 0x08, 0x62, 0x75, 0x79, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x14, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x47, 0x6f, 0x6f, 0x64, 0x73, 0x42,
	0x75, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x07, 0x62, 0x75, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x22,
	0x7e, 0x0a, 0x0b, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x42, 0x75, 0x79, 0x52, 0x65, 0x71, 0x12, 0x20,
	0x0a, 0x0c, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x5f, 0x62, 0x75, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x42, 0x75, 0x79, 0x49, 0x64,
	0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x37, 0x0a, 0x0a, 0x73, 0x74, 0x79, 0x6c, 0x65, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x18, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x53, 0x54, 0x4f, 0x52, 0x45, 0x5f, 0x53, 0x48, 0x4f, 0x57, 0x5f, 0x53,
	0x54, 0x59, 0x4c, 0x45, 0x52, 0x09, 0x73, 0x74, 0x79, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x22,
	0x98, 0x01, 0x0a, 0x0b, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x42, 0x75, 0x79, 0x52, 0x73, 0x70, 0x12,
	0x20, 0x0a, 0x03, 0x72, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x03, 0x72, 0x65,
	0x74, 0x12, 0x20, 0x0a, 0x0c, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x5f, 0x62, 0x75, 0x79, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x42, 0x75,
	0x79, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x2f, 0x0a, 0x0b, 0x72, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x52, 0x0a,
	0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x74, 0x0a, 0x11, 0x45, 0x78,
	0x70, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x4e, 0x74, 0x66, 0x12,
	0x17, 0x0a, 0x07, 0x63, 0x75, 0x72, 0x5f, 0x65, 0x78, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x06, 0x63, 0x75, 0x72, 0x45, 0x78, 0x70, 0x12, 0x46, 0x0a, 0x11, 0x6c, 0x65, 0x76, 0x65,
	0x6c, 0x5f, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x45, 0x78, 0x70,
	0x4c, 0x65, 0x76, 0x65, 0x6c, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52,
	0x0f, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x49, 0x6e, 0x66, 0x6f,
	0x22, 0x2f, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x65, 0x71, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49,
	0x64, 0x22, 0x4b, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x73, 0x70, 0x12, 0x37, 0x0a, 0x0b, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x5f,
	0x69, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x42, 0x61, 0x73, 0x65, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x0a, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0xa0,
	0x01, 0x0a, 0x0a, 0x55, 0x73, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x65, 0x71, 0x12, 0x2d, 0x0a,
	0x09, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x10, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x49, 0x74, 0x65, 0x6d, 0x42, 0x61,
	0x73, 0x65, 0x52, 0x08, 0x69, 0x74, 0x65, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x33, 0x0a, 0x08,
	0x73, 0x72, 0x63, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x18,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x49, 0x54, 0x45, 0x4d, 0x5f, 0x53, 0x4f, 0x55,
	0x52, 0x43, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x52, 0x07, 0x73, 0x72, 0x63, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x2e, 0x0a, 0x07, 0x73, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x14, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x53, 0x54, 0x4f, 0x52,
	0x41, 0x47, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x52, 0x07, 0x73, 0x74, 0x6f, 0x72, 0x61, 0x67,
	0x65, 0x22, 0x2e, 0x0a, 0x0a, 0x55, 0x73, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x73, 0x70, 0x12,
	0x20, 0x0a, 0x03, 0x72, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x03, 0x72, 0x65,
	0x74, 0x22, 0x2a, 0x0a, 0x11, 0x46, 0x69, 0x72, 0x73, 0x74, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x48,
	0x61, 0x6c, 0x6c, 0x52, 0x65, 0x71, 0x12, 0x15, 0x0a, 0x06, 0x69, 0x73, 0x5f, 0x72, 0x65, 0x67,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x05, 0x69, 0x73, 0x52, 0x65, 0x67, 0x22, 0x81, 0x01,
	0x0a, 0x11, 0x46, 0x69, 0x72, 0x73, 0x74, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x48, 0x61, 0x6c, 0x6c,
	0x52, 0x73, 0x70, 0x12, 0x37, 0x0a, 0x0b, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x5f, 0x69, 0x6e,
	0x66, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x42, 0x61, 0x73, 0x65, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x0a, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x33, 0x0a, 0x0a,
	0x70, 0x6f, 0x70, 0x75, 0x70, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x14, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x41, 0x6e, 0x6e, 0x50, 0x6f, 0x70,
	0x75, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x09, 0x70, 0x6f, 0x70, 0x75, 0x70, 0x49, 0x6e, 0x66,
	0x6f, 0x22, 0x7c, 0x0a, 0x10, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x4d, 0x75, 0x6c, 0x74, 0x69, 0x42,
	0x75, 0x79, 0x52, 0x65, 0x71, 0x12, 0x2f, 0x0a, 0x0a, 0x67, 0x6f, 0x6f, 0x64, 0x73, 0x5f, 0x6c,
	0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x49, 0x74, 0x65, 0x6d, 0x42, 0x61, 0x73, 0x65, 0x52, 0x09, 0x67, 0x6f, 0x6f,
	0x64, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x37, 0x0a, 0x0a, 0x73, 0x74, 0x79, 0x6c, 0x65, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x18, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x53, 0x54, 0x4f, 0x52, 0x45, 0x5f, 0x53, 0x48, 0x4f, 0x57, 0x5f, 0x53,
	0x54, 0x59, 0x4c, 0x45, 0x52, 0x09, 0x73, 0x74, 0x79, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x22,
	0x94, 0x01, 0x0a, 0x10, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x4d, 0x75, 0x6c, 0x74, 0x69, 0x42, 0x75,
	0x79, 0x52, 0x73, 0x70, 0x12, 0x20, 0x0a, 0x03, 0x72, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x52, 0x03, 0x72, 0x65, 0x74, 0x12, 0x2d, 0x0a, 0x09, 0x67, 0x6f, 0x6f, 0x73, 0x5f, 0x6c,
	0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x49, 0x74, 0x65, 0x6d, 0x42, 0x61, 0x73, 0x65, 0x52, 0x08, 0x67, 0x6f, 0x6f,
	0x73, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x2f, 0x0a, 0x0b, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f,
	0x69, 0x6e, 0x66, 0x6f, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x52, 0x0a, 0x72, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x10, 0x0a, 0x0e, 0x47, 0x65, 0x74, 0x53, 0x74, 0x61,
	0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x22, 0x58, 0x0a, 0x0e, 0x47, 0x65, 0x74, 0x53,
	0x74, 0x61, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x73, 0x70, 0x12, 0x20, 0x0a, 0x03, 0x72, 0x65,
	0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x03, 0x72, 0x65, 0x74, 0x12, 0x24, 0x0a, 0x04,
	0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x6c, 0x69,
	0x73, 0x74, 0x22, 0x39, 0x0a, 0x11, 0x53, 0x74, 0x61, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x4e, 0x74, 0x66, 0x12, 0x24, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x53,
	0x74, 0x61, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0x29, 0x0a,
	0x10, 0x47, 0x65, 0x74, 0x52, 0x6f, 0x64, 0x52, 0x69, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65,
	0x71, 0x12, 0x15, 0x0a, 0x06, 0x72, 0x69, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x05, 0x72, 0x69, 0x67, 0x49, 0x64, 0x22, 0x63, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x52,
	0x6f, 0x64, 0x52, 0x69, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x73, 0x70, 0x12, 0x20, 0x0a, 0x03,
	0x72, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x03, 0x72, 0x65, 0x74, 0x12, 0x2d,
	0x0a, 0x08, 0x72, 0x69, 0x67, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x12, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x6f, 0x64, 0x52, 0x69, 0x67,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x07, 0x72, 0x69, 0x67, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x44, 0x0a,
	0x13, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x6f, 0x64, 0x52, 0x69, 0x67, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x65, 0x71, 0x12, 0x2d, 0x0a, 0x08, 0x72, 0x69, 0x67, 0x5f, 0x69, 0x6e, 0x66, 0x6f,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x52, 0x6f, 0x64, 0x52, 0x69, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x07, 0x72, 0x69, 0x67, 0x49,
	0x6e, 0x66, 0x6f, 0x22, 0x66, 0x0a, 0x13, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x6f, 0x64,
	0x52, 0x69, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x73, 0x70, 0x12, 0x20, 0x0a, 0x03, 0x72, 0x65,
	0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x03, 0x72, 0x65, 0x74, 0x12, 0x2d, 0x0a, 0x08,
	0x72, 0x69, 0x67, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x6f, 0x64, 0x52, 0x69, 0x67, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x07, 0x72, 0x69, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x2c, 0x0a, 0x13, 0x44,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x52, 0x6f, 0x64, 0x52, 0x69, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x52,
	0x65, 0x71, 0x12, 0x15, 0x0a, 0x06, 0x72, 0x69, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x05, 0x72, 0x69, 0x67, 0x49, 0x64, 0x22, 0x4e, 0x0a, 0x13, 0x44, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x52, 0x6f, 0x64, 0x52, 0x69, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x73, 0x70,
	0x12, 0x20, 0x0a, 0x03, 0x72, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x03, 0x72,
	0x65, 0x74, 0x12, 0x15, 0x0a, 0x06, 0x72, 0x69, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x05, 0x72, 0x69, 0x67, 0x49, 0x64, 0x22, 0x40, 0x0a, 0x0f, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x52, 0x6f, 0x64, 0x52, 0x69, 0x67, 0x4e, 0x74, 0x66, 0x12, 0x2d, 0x0a, 0x08,
	0x72, 0x6f, 0x64, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x12,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x6f, 0x64, 0x42, 0x61, 0x67, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x07, 0x72, 0x6f, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0xa8, 0x01, 0x0a, 0x13,
	0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x44, 0x75, 0x72, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79,
	0x52, 0x65, 0x71, 0x12, 0x15, 0x0a, 0x06, 0x72, 0x69, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x05, 0x72, 0x69, 0x67, 0x49, 0x64, 0x12, 0x3f, 0x0a, 0x06, 0x63, 0x68,
	0x61, 0x6e, 0x67, 0x65, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x68, 0x61, 0x6c,
	0x6c, 0x50, 0x42, 0x2e, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x44, 0x75, 0x72, 0x61, 0x62, 0x69,
	0x6c, 0x69, 0x74, 0x79, 0x52, 0x65, 0x71, 0x2e, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x52, 0x06, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x1a, 0x39, 0x0a, 0x0b, 0x43,
	0x68, 0x61, 0x6e, 0x67, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65,
	0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x66, 0x0a, 0x13, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79,
	0x44, 0x75, 0x72, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x52, 0x73, 0x70, 0x12, 0x20, 0x0a,
	0x03, 0x72, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x03, 0x72, 0x65, 0x74, 0x12,
	0x2d, 0x0a, 0x08, 0x72, 0x6f, 0x64, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x12, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x6f, 0x64, 0x42, 0x61,
	0x67, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x07, 0x72, 0x6f, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x41,
	0x0a, 0x12, 0x4d, 0x61, 0x69, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x52, 0x6f, 0x64, 0x49, 0x74, 0x65,
	0x6d, 0x52, 0x65, 0x71, 0x12, 0x15, 0x0a, 0x06, 0x72, 0x69, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x72, 0x69, 0x67, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x73,
	0x69, 0x74, 0x49, 0x64, 0x18, 0x02, 0x20, 0x03, 0x28, 0x05, 0x52, 0x05, 0x73, 0x69, 0x74, 0x49,
	0x64, 0x22, 0x89, 0x01, 0x0a, 0x12, 0x4d, 0x61, 0x69, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x52, 0x6f,
	0x64, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x73, 0x70, 0x12, 0x20, 0x0a, 0x03, 0x72, 0x65, 0x74, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x03, 0x72, 0x65, 0x74, 0x12, 0x2d, 0x0a, 0x08, 0x72, 0x6f,
	0x64, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x6f, 0x64, 0x42, 0x61, 0x67, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x07, 0x72, 0x6f, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x22, 0x0a, 0x04, 0x63, 0x6f, 0x73,
	0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x52, 0x04, 0x63, 0x6f, 0x73, 0x74, 0x22, 0x6e, 0x0a,
	0x16, 0x4d, 0x61, 0x69, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x53, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65,
	0x49, 0x74, 0x65, 0x6d, 0x52, 0x65, 0x71, 0x12, 0x33, 0x0a, 0x0a, 0x73, 0x74, 0x6f, 0x72, 0x65,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x53, 0x54, 0x4f, 0x52, 0x41, 0x47, 0x45, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x52, 0x09, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1f, 0x0a, 0x0b,
	0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x0a, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x22, 0x5e, 0x0a,
	0x16, 0x4d, 0x61, 0x69, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x53, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65,
	0x49, 0x74, 0x65, 0x6d, 0x52, 0x73, 0x70, 0x12, 0x20, 0x0a, 0x03, 0x72, 0x65, 0x74, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x52, 0x03, 0x72, 0x65, 0x74, 0x12, 0x22, 0x0a, 0x04, 0x63, 0x6f, 0x73,
	0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x52, 0x04, 0x63, 0x6f, 0x73, 0x74, 0x22, 0x68, 0x0a,
	0x0b, 0x53, 0x65, 0x6c, 0x6c, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x65, 0x71, 0x12, 0x33, 0x0a, 0x0a,
	0x73, 0x74, 0x6f, 0x72, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x14, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x53, 0x54, 0x4f, 0x52, 0x41, 0x47,
	0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x52, 0x09, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x24, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x10, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x49, 0x74, 0x65, 0x6d, 0x42, 0x61, 0x73,
	0x65, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0x57, 0x0a, 0x0b, 0x53, 0x65, 0x6c, 0x6c, 0x49,
	0x74, 0x65, 0x6d, 0x52, 0x73, 0x70, 0x12, 0x20, 0x0a, 0x03, 0x72, 0x65, 0x74, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x52, 0x03, 0x72, 0x65, 0x74, 0x12, 0x26, 0x0a, 0x06, 0x72, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x52, 0x06, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x22, 0x3f, 0x0a, 0x0d, 0x47, 0x65, 0x74, 0x54, 0x72, 0x69, 0x70, 0x52, 0x6f, 0x64, 0x52, 0x65,
	0x71, 0x12, 0x2e, 0x0a, 0x07, 0x73, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x14, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x53, 0x54, 0x4f, 0x52,
	0x41, 0x47, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x52, 0x07, 0x73, 0x74, 0x6f, 0x72, 0x61, 0x67,
	0x65, 0x22, 0x59, 0x0a, 0x0d, 0x47, 0x65, 0x74, 0x54, 0x72, 0x69, 0x70, 0x52, 0x6f, 0x64, 0x52,
	0x73, 0x70, 0x12, 0x20, 0x0a, 0x03, 0x72, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x0e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52,
	0x03, 0x72, 0x65, 0x74, 0x12, 0x26, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x12, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x6f, 0x64, 0x42,
	0x61, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0x37, 0x0a, 0x0e,
	0x4c, 0x6f, 0x61, 0x64, 0x54, 0x72, 0x69, 0x70, 0x52, 0x6f, 0x64, 0x52, 0x65, 0x71, 0x12, 0x15,
	0x0a, 0x06, 0x72, 0x69, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05,
	0x72, 0x69, 0x67, 0x49, 0x64, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x02, 0x69, 0x64, 0x22, 0x81, 0x01, 0x0a, 0x0e, 0x4c, 0x6f, 0x61, 0x64, 0x54, 0x72,
	0x69, 0x70, 0x52, 0x6f, 0x64, 0x52, 0x73, 0x70, 0x12, 0x20, 0x0a, 0x03, 0x72, 0x65, 0x74, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x03, 0x72, 0x65, 0x74, 0x12, 0x15, 0x0a, 0x06, 0x72, 0x69,
	0x67, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x72, 0x69, 0x67, 0x49,
	0x64, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x26, 0x0a, 0x04, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x12, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x6f, 0x64, 0x42, 0x61, 0x67, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x04, 0x69, 0x6e, 0x66, 0x6f, 0x22, 0x27, 0x0a, 0x0e, 0x53, 0x61, 0x76,
	0x65, 0x54, 0x72, 0x69, 0x70, 0x52, 0x6f, 0x64, 0x52, 0x65, 0x71, 0x12, 0x15, 0x0a, 0x06, 0x72,
	0x69, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x72, 0x69, 0x67,
	0x49, 0x64, 0x22, 0x81, 0x01, 0x0a, 0x0e, 0x53, 0x61, 0x76, 0x65, 0x54, 0x72, 0x69, 0x70, 0x52,
	0x6f, 0x64, 0x52, 0x73, 0x70, 0x12, 0x20, 0x0a, 0x03, 0x72, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x52, 0x03, 0x72, 0x65, 0x74, 0x12, 0x15, 0x0a, 0x06, 0x72, 0x69, 0x67, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x72, 0x69, 0x67, 0x49, 0x64, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x12, 0x26,
	0x0a, 0x04, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x6f, 0x64, 0x42, 0x61, 0x67, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x04, 0x69, 0x6e, 0x66, 0x6f, 0x22, 0x72, 0x0a, 0x11, 0x53, 0x61, 0x76, 0x65, 0x4e, 0x65,
	0x77, 0x54, 0x72, 0x69, 0x70, 0x52, 0x6f, 0x64, 0x52, 0x65, 0x71, 0x12, 0x12, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x2c, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e,
	0x68, 0x61, 0x6c, 0x6c, 0x50, 0x42, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x72, 0x69,
	0x70, 0x52, 0x6f, 0x64, 0x52, 0x65, 0x71, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x1b, 0x0a,
	0x09, 0x62, 0x61, 0x67, 0x5f, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x08, 0x62, 0x61, 0x67, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x22, 0x8a, 0x01, 0x0a, 0x11, 0x53,
	0x61, 0x76, 0x65, 0x4e, 0x65, 0x77, 0x54, 0x72, 0x69, 0x70, 0x52, 0x6f, 0x64, 0x52, 0x73, 0x70,
	0x12, 0x20, 0x0a, 0x03, 0x72, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x03, 0x72,
	0x65, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x26, 0x0a, 0x04, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x12, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x6f, 0x64, 0x42, 0x61, 0x67,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x69, 0x6e, 0x66, 0x6f, 0x12, 0x1b, 0x0a, 0x09, 0x62, 0x61,
	0x67, 0x5f, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x62,
	0x61, 0x67, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x22, 0x3c, 0x0a, 0x0d, 0x50, 0x75, 0x74, 0x54, 0x72,
	0x69, 0x70, 0x52, 0x6f, 0x64, 0x52, 0x65, 0x71, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x62, 0x61, 0x67, 0x5f,
	0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x62, 0x61, 0x67,
	0x49, 0x6e, 0x64, 0x65, 0x78, 0x22, 0x86, 0x01, 0x0a, 0x0d, 0x50, 0x75, 0x74, 0x54, 0x72, 0x69,
	0x70, 0x52, 0x6f, 0x64, 0x52, 0x73, 0x70, 0x12, 0x20, 0x0a, 0x03, 0x72, 0x65, 0x74, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x52, 0x03, 0x72, 0x65, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x62, 0x61, 0x67,
	0x5f, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x62, 0x61,
	0x67, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x12, 0x26, 0x0a, 0x04, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x6f,
	0x64, 0x42, 0x61, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x69, 0x6e, 0x66, 0x6f, 0x22, 0x1f,
	0x0a, 0x0d, 0x44, 0x65, 0x6c, 0x54, 0x72, 0x69, 0x70, 0x52, 0x6f, 0x64, 0x52, 0x65, 0x71, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x22,
	0x69, 0x0a, 0x0d, 0x44, 0x65, 0x6c, 0x54, 0x72, 0x69, 0x70, 0x52, 0x6f, 0x64, 0x52, 0x73, 0x70,
	0x12, 0x20, 0x0a, 0x03, 0x72, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x03, 0x72,
	0x65, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x26, 0x0a, 0x04, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x12, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x6f, 0x64, 0x42, 0x61, 0x67,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x69, 0x6e, 0x66, 0x6f, 0x22, 0x21, 0x0a, 0x0f, 0x53, 0x70,
	0x6c, 0x69, 0x74, 0x54, 0x72, 0x69, 0x70, 0x52, 0x6f, 0x64, 0x52, 0x65, 0x71, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x22, 0x43, 0x0a,
	0x0f, 0x53, 0x70, 0x6c, 0x69, 0x74, 0x54, 0x72, 0x69, 0x70, 0x52, 0x6f, 0x64, 0x52, 0x73, 0x70,
	0x12, 0x20, 0x0a, 0x03, 0x72, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x03, 0x72,
	0x65, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02,
	0x69, 0x64, 0x22, 0x9e, 0x01, 0x0a, 0x10, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x72, 0x69,
	0x70, 0x52, 0x6f, 0x64, 0x52, 0x65, 0x71, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x73, 0x69, 0x74, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x73, 0x69, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x69, 0x74, 0x65,
	0x6d, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x69, 0x74, 0x65, 0x6d,
	0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x69,
	0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63,
	0x65, 0x49, 0x64, 0x12, 0x2e, 0x0a, 0x07, 0x73, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x53, 0x54,
	0x4f, 0x52, 0x41, 0x47, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x52, 0x07, 0x73, 0x74, 0x6f, 0x72,
	0x61, 0x67, 0x65, 0x22, 0xb8, 0x01, 0x0a, 0x10, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x72,
	0x69, 0x70, 0x52, 0x6f, 0x64, 0x52, 0x73, 0x70, 0x12, 0x20, 0x0a, 0x03, 0x72, 0x65, 0x74, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x03, 0x72, 0x65, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x73, 0x69,
	0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x73, 0x69, 0x74, 0x12, 0x17, 0x0a, 0x07,
	0x69, 0x74, 0x65, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x69,
	0x74, 0x65, 0x6d, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63,
	0x65, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x69, 0x6e, 0x73, 0x74,
	0x61, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x04, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x6f,
	0x64, 0x42, 0x61, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x69, 0x6e, 0x66, 0x6f, 0x22, 0x69,
	0x0a, 0x15, 0x42, 0x61, 0x74, 0x63, 0x68, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x72, 0x69,
	0x70, 0x52, 0x6f, 0x64, 0x52, 0x65, 0x71, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x12, 0x2c, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x68, 0x61, 0x6c, 0x6c, 0x50, 0x42, 0x2e, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x72, 0x69, 0x70, 0x52, 0x6f, 0x64, 0x52, 0x65, 0x71, 0x52,
	0x04, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x61, 0x0a, 0x15, 0x42, 0x61, 0x74,
	0x63, 0x68, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x72, 0x69, 0x70, 0x52, 0x6f, 0x64, 0x52,
	0x73, 0x70, 0x12, 0x20, 0x0a, 0x03, 0x72, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x0e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52,
	0x03, 0x72, 0x65, 0x74, 0x12, 0x26, 0x0a, 0x04, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x12, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x6f, 0x64, 0x42,
	0x61, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x69, 0x6e, 0x66, 0x6f, 0x22, 0x3a, 0x0a, 0x0d,
	0x47, 0x65, 0x74, 0x54, 0x72, 0x69, 0x70, 0x42, 0x61, 0x67, 0x52, 0x65, 0x71, 0x12, 0x29, 0x0a,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x52, 0x49, 0x50, 0x5f, 0x42, 0x41, 0x47, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x22, 0x82, 0x01, 0x0a, 0x0d, 0x47, 0x65, 0x74,
	0x54, 0x72, 0x69, 0x70, 0x42, 0x61, 0x67, 0x52, 0x73, 0x70, 0x12, 0x20, 0x0a, 0x03, 0x72, 0x65,
	0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x03, 0x72, 0x65, 0x74, 0x12, 0x29, 0x0a, 0x04,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x52, 0x49, 0x50, 0x5f, 0x42, 0x41, 0x47, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x24, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18,
	0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x49,
	0x74, 0x65, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0xc1, 0x01,
	0x0a, 0x10, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x54, 0x72, 0x69, 0x70, 0x42, 0x61, 0x67, 0x52,
	0x65, 0x71, 0x12, 0x29, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x52, 0x49, 0x50, 0x5f, 0x42,
	0x41, 0x47, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x32, 0x0a,
	0x07, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x18,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x52, 0x49, 0x50, 0x5f, 0x42, 0x41, 0x47,
	0x5f, 0x4f, 0x50, 0x45, 0x52, 0x41, 0x54, 0x45, 0x52, 0x07, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74,
	0x65, 0x12, 0x17, 0x0a, 0x07, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x06, 0x69, 0x74, 0x65, 0x6d, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x69, 0x6e,
	0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x22, 0xac, 0x01, 0x0a, 0x10, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x54, 0x72, 0x69, 0x70,
	0x42, 0x61, 0x67, 0x52, 0x73, 0x70, 0x12, 0x20, 0x0a, 0x03, 0x72, 0x65, 0x74, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x52, 0x03, 0x72, 0x65, 0x74, 0x12, 0x29, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x54, 0x52, 0x49, 0x50, 0x5f, 0x42, 0x41, 0x47, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x52, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x12, 0x32, 0x0a, 0x07, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x18, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x52,
	0x49, 0x50, 0x5f, 0x42, 0x41, 0x47, 0x5f, 0x4f, 0x50, 0x45, 0x52, 0x41, 0x54, 0x45, 0x52, 0x07,
	0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x69, 0x74, 0x65, 0x6d, 0x5f,
	0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x69, 0x74, 0x65, 0x6d, 0x49, 0x64,
	0x22, 0x43, 0x0a, 0x12, 0x54, 0x72, 0x69, 0x70, 0x42, 0x61, 0x67, 0x51, 0x75, 0x69, 0x63, 0x6b,
	0x42, 0x75, 0x79, 0x52, 0x65, 0x71, 0x12, 0x17, 0x0a, 0x07, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x69,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x69, 0x74, 0x65, 0x6d, 0x49, 0x64, 0x12,
	0x14, 0x0a, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x65, 0x0a, 0x12, 0x54, 0x72, 0x69, 0x70, 0x42, 0x61, 0x67,
	0x51, 0x75, 0x69, 0x63, 0x6b, 0x42, 0x75, 0x79, 0x52, 0x73, 0x70, 0x12, 0x20, 0x0a, 0x03, 0x72,
	0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x03, 0x72, 0x65, 0x74, 0x12, 0x17, 0x0a,
	0x07, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06,
	0x69, 0x74, 0x65, 0x6d, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x69, 0x0a, 0x0d,
	0x54, 0x72, 0x69, 0x70, 0x42, 0x61, 0x67, 0x55, 0x73, 0x65, 0x52, 0x65, 0x71, 0x12, 0x29, 0x0a,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x52, 0x49, 0x50, 0x5f, 0x42, 0x41, 0x47, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x69, 0x74, 0x65, 0x6d,
	0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x69, 0x74, 0x65, 0x6d, 0x49,
	0x64, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x8b, 0x01, 0x0a, 0x0d, 0x54, 0x72, 0x69, 0x70,
	0x42, 0x61, 0x67, 0x55, 0x73, 0x65, 0x52, 0x73, 0x70, 0x12, 0x20, 0x0a, 0x03, 0x72, 0x65, 0x74,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x03, 0x72, 0x65, 0x74, 0x12, 0x29, 0x0a, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x54, 0x52, 0x49, 0x50, 0x5f, 0x42, 0x41, 0x47, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x69,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x69, 0x74, 0x65, 0x6d, 0x49, 0x64, 0x12,
	0x14, 0x0a, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x28, 0x0a, 0x12, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x46, 0x6f,
	0x72, 0x62, 0x69, 0x64, 0x57, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x71, 0x12, 0x12, 0x0a, 0x04, 0x77,
	0x6f, 0x72, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x77, 0x6f, 0x72, 0x64, 0x22,
	0x52, 0x0a, 0x12, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x46, 0x6f, 0x72, 0x62, 0x69, 0x64, 0x57, 0x6f,
	0x72, 0x64, 0x52, 0x73, 0x70, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x73, 0x5f, 0x66, 0x6f, 0x72, 0x62,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x69, 0x73, 0x46, 0x6f, 0x72, 0x62,
	0x69, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x66, 0x6f, 0x72, 0x62, 0x69, 0x64, 0x5f, 0x77, 0x6f, 0x72,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x66, 0x6f, 0x72, 0x62, 0x69, 0x64, 0x57,
	0x6f, 0x72, 0x64, 0x22, 0x15, 0x0a, 0x13, 0x55, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x41, 0x6c, 0x6c,
	0x54, 0x72, 0x69, 0x70, 0x42, 0x61, 0x67, 0x52, 0x65, 0x71, 0x22, 0x37, 0x0a, 0x13, 0x55, 0x6e,
	0x6c, 0x6f, 0x61, 0x64, 0x41, 0x6c, 0x6c, 0x54, 0x72, 0x69, 0x70, 0x42, 0x61, 0x67, 0x52, 0x73,
	0x70, 0x12, 0x20, 0x0a, 0x03, 0x72, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x03,
	0x72, 0x65, 0x74, 0x22, 0x0d, 0x0a, 0x0b, 0x49, 0x74, 0x65, 0x6d, 0x48, 0x65, 0x61, 0x70, 0x52,
	0x65, 0x71, 0x22, 0xb0, 0x01, 0x0a, 0x0b, 0x49, 0x74, 0x65, 0x6d, 0x48, 0x65, 0x61, 0x70, 0x52,
	0x73, 0x70, 0x12, 0x20, 0x0a, 0x03, 0x72, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x0e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52,
	0x03, 0x72, 0x65, 0x74, 0x12, 0x41, 0x0a, 0x0a, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x68, 0x65, 0x61,
	0x70, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x68, 0x61, 0x6c, 0x6c, 0x50,
	0x42, 0x2e, 0x49, 0x74, 0x65, 0x6d, 0x48, 0x65, 0x61, 0x70, 0x52, 0x73, 0x70, 0x2e, 0x49, 0x74,
	0x65, 0x6d, 0x48, 0x65, 0x61, 0x70, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x09, 0x69, 0x74,
	0x65, 0x6d, 0x48, 0x65, 0x61, 0x70, 0x73, 0x1a, 0x3c, 0x0a, 0x0e, 0x49, 0x74, 0x65, 0x6d, 0x48,
	0x65, 0x61, 0x70, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xa0, 0x01, 0x0a, 0x14, 0x49, 0x74, 0x65, 0x6d, 0x48, 0x65,
	0x61, 0x70, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x12, 0x4a,
	0x0a, 0x0a, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x68, 0x65, 0x61, 0x70, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x68, 0x61, 0x6c, 0x6c, 0x50, 0x42, 0x2e, 0x49, 0x74, 0x65, 0x6d,
	0x48, 0x65, 0x61, 0x70, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x79,
	0x2e, 0x49, 0x74, 0x65, 0x6d, 0x48, 0x65, 0x61, 0x70, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52,
	0x09, 0x69, 0x74, 0x65, 0x6d, 0x48, 0x65, 0x61, 0x70, 0x73, 0x1a, 0x3c, 0x0a, 0x0e, 0x49, 0x74,
	0x65, 0x6d, 0x48, 0x65, 0x61, 0x70, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03,
	0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14,
	0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x4e, 0x0a, 0x0f, 0x52, 0x65, 0x61, 0x6c,
	0x4e, 0x61, 0x6d, 0x65, 0x41, 0x75, 0x74, 0x68, 0x52, 0x65, 0x71, 0x12, 0x1b, 0x0a, 0x09, 0x72,
	0x65, 0x61, 0x6c, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x72, 0x65, 0x61, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0b, 0x69, 0x64, 0x5f, 0x63,
	0x61, 0x72, 0x64, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x69,
	0x64, 0x43, 0x61, 0x72, 0x64, 0x4e, 0x75, 0x6d, 0x22, 0x33, 0x0a, 0x0f, 0x52, 0x65, 0x61, 0x6c,
	0x4e, 0x61, 0x6d, 0x65, 0x41, 0x75, 0x74, 0x68, 0x52, 0x73, 0x70, 0x12, 0x20, 0x0a, 0x03, 0x72,
	0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x03, 0x72, 0x65, 0x74, 0x22, 0x4e, 0x0a,
	0x10, 0x41, 0x6e, 0x74, 0x69, 0x41, 0x64, 0x64, 0x69, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x74,
	0x66, 0x12, 0x19, 0x0a, 0x08, 0x69, 0x73, 0x5f, 0x63, 0x68, 0x69, 0x6c, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x07, 0x69, 0x73, 0x43, 0x68, 0x69, 0x6c, 0x64, 0x12, 0x1f, 0x0a, 0x0b,
	0x65, 0x78, 0x63, 0x65, 0x65, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x0a, 0x65, 0x78, 0x63, 0x65, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x22, 0x32, 0x0a,
	0x0e, 0x54, 0x65, 0x6d, 0x70, 0x43, 0x61, 0x73, 0x68, 0x42, 0x75, 0x79, 0x52, 0x65, 0x71, 0x12,
	0x20, 0x0a, 0x0c, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x5f, 0x62, 0x75, 0x79, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x42, 0x75, 0x79, 0x49,
	0x64, 0x22, 0x85, 0x01, 0x0a, 0x0e, 0x54, 0x65, 0x6d, 0x70, 0x43, 0x61, 0x73, 0x68, 0x42, 0x75,
	0x79, 0x52, 0x73, 0x70, 0x12, 0x20, 0x0a, 0x03, 0x72, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x52, 0x03, 0x72, 0x65, 0x74, 0x12, 0x20, 0x0a, 0x0c, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x5f,
	0x62, 0x75, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x73, 0x74,
	0x6f, 0x72, 0x65, 0x42, 0x75, 0x79, 0x49, 0x64, 0x12, 0x2f, 0x0a, 0x0b, 0x72, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x52, 0x0a, 0x72,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x34, 0x0a, 0x16, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x47, 0x75, 0x69, 0x64, 0x65, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73,
	0x52, 0x65, 0x71, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x22,
	0x56, 0x0a, 0x16, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x47, 0x75, 0x69, 0x64, 0x65, 0x50, 0x72,
	0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x52, 0x73, 0x70, 0x12, 0x20, 0x0a, 0x03, 0x72, 0x65, 0x74,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x03, 0x72, 0x65, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x70,
	0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70,
	0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x22, 0x14, 0x0a, 0x12, 0x43, 0x6f, 0x6e, 0x74, 0x69,
	0x6e, 0x75, 0x6f, 0x75, 0x73, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x52, 0x65, 0x71, 0x22, 0x65, 0x0a,
	0x12, 0x43, 0x6f, 0x6e, 0x74, 0x69, 0x6e, 0x75, 0x6f, 0x75, 0x73, 0x4c, 0x6f, 0x67, 0x69, 0x6e,
	0x52, 0x73, 0x70, 0x12, 0x20, 0x0a, 0x03, 0x72, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x0e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x52, 0x03, 0x72, 0x65, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f,
	0x74, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x54, 0x73, 0x12, 0x10, 0x0a, 0x03, 0x64, 0x61, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x03, 0x64, 0x61, 0x79, 0x22, 0x1a, 0x0a, 0x18, 0x43, 0x6f, 0x6e, 0x74, 0x69, 0x6e, 0x75, 0x6f,
	0x75, 0x73, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x52, 0x65, 0x71,
	0x22, 0x93, 0x01, 0x0a, 0x18, 0x43, 0x6f, 0x6e, 0x74, 0x69, 0x6e, 0x75, 0x6f, 0x75, 0x73, 0x4c,
	0x6f, 0x67, 0x69, 0x6e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x52, 0x73, 0x70, 0x12, 0x20, 0x0a,
	0x03, 0x72, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x03, 0x72, 0x65, 0x74, 0x12,
	0x1b, 0x0a, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x73, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x08, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x73, 0x12, 0x10, 0x0a, 0x03,
	0x64, 0x61, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x64, 0x61, 0x79, 0x12, 0x26,
	0x0a, 0x06, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x52, 0x06,
	0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x22, 0x8f, 0x01, 0x0a, 0x0a, 0x52, 0x65, 0x64, 0x44, 0x6f,
	0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x39, 0x0a, 0x0b, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x18, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x4d, 0x4f, 0x44, 0x55, 0x4c, 0x45, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x52, 0x0a, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x26, 0x0a, 0x0f, 0x73, 0x75, 0x62, 0x5f, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x73, 0x75, 0x62, 0x4d, 0x6f,
	0x64, 0x75, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1e, 0x0a, 0x0b, 0x68, 0x61, 0x73, 0x5f,
	0x72, 0x65, 0x64, 0x5f, 0x64, 0x6f, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x68,
	0x61, 0x73, 0x52, 0x65, 0x64, 0x44, 0x6f, 0x74, 0x22, 0x77, 0x0a, 0x12, 0x43, 0x6c, 0x65, 0x61,
	0x72, 0x52, 0x65, 0x64, 0x44, 0x6f, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x39,
	0x0a, 0x0b, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x18, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x55, 0x53, 0x45,
	0x52, 0x5f, 0x4d, 0x4f, 0x44, 0x55, 0x4c, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x52, 0x0a, 0x6d,
	0x6f, 0x64, 0x75, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x26, 0x0a, 0x0f, 0x73, 0x75, 0x62,
	0x5f, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x0d, 0x73, 0x75, 0x62, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x22, 0x64, 0x0a, 0x13, 0x43, 0x6c, 0x65, 0x61, 0x72, 0x52, 0x65, 0x64, 0x44, 0x6f, 0x74,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x20, 0x0a, 0x03, 0x72, 0x65, 0x74, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x03, 0x72, 0x65, 0x74, 0x12, 0x2b, 0x0a, 0x07, 0x72, 0x65,
	0x64, 0x5f, 0x64, 0x6f, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x68, 0x61,
	0x6c, 0x6c, 0x50, 0x42, 0x2e, 0x52, 0x65, 0x64, 0x44, 0x6f, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52,
	0x06, 0x72, 0x65, 0x64, 0x44, 0x6f, 0x74, 0x22, 0x1c, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x50, 0x6c,
	0x61, 0x79, 0x65, 0x72, 0x41, 0x6c, 0x6c, 0x52, 0x65, 0x64, 0x44, 0x6f, 0x74, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x6e, 0x0a, 0x1b, 0x47, 0x65, 0x74, 0x50, 0x6c, 0x61, 0x79,
	0x65, 0x72, 0x41, 0x6c, 0x6c, 0x52, 0x65, 0x64, 0x44, 0x6f, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x20, 0x0a, 0x03, 0x72, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x52, 0x03, 0x72, 0x65, 0x74, 0x12, 0x2d, 0x0a, 0x08, 0x72, 0x65, 0x64, 0x5f, 0x64, 0x6f,
	0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x68, 0x61, 0x6c, 0x6c, 0x50,
	0x42, 0x2e, 0x52, 0x65, 0x64, 0x44, 0x6f, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x07, 0x72, 0x65,
	0x64, 0x44, 0x6f, 0x74, 0x73, 0x22, 0x5a, 0x0a, 0x1d, 0x47, 0x65, 0x74, 0x50, 0x6c, 0x61, 0x79,
	0x65, 0x72, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x64, 0x44, 0x6f, 0x74, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x39, 0x0a, 0x0b, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x18, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x4d, 0x4f, 0x44, 0x55, 0x4c, 0x45,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x52, 0x0a, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x22, 0x71, 0x0a, 0x1e, 0x47, 0x65, 0x74, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x4d, 0x6f,
	0x64, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x64, 0x44, 0x6f, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x20, 0x0a, 0x03, 0x72, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x0e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x52, 0x03, 0x72, 0x65, 0x74, 0x12, 0x2d, 0x0a, 0x08, 0x72, 0x65, 0x64, 0x5f, 0x64, 0x6f, 0x74,
	0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x68, 0x61, 0x6c, 0x6c, 0x50, 0x42,
	0x2e, 0x52, 0x65, 0x64, 0x44, 0x6f, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x07, 0x72, 0x65, 0x64,
	0x44, 0x6f, 0x74, 0x73, 0x22, 0x43, 0x0a, 0x12, 0x52, 0x65, 0x64, 0x44, 0x6f, 0x74, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x12, 0x2d, 0x0a, 0x08, 0x72, 0x65,
	0x64, 0x5f, 0x64, 0x6f, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x68,
	0x61, 0x6c, 0x6c, 0x50, 0x42, 0x2e, 0x52, 0x65, 0x64, 0x44, 0x6f, 0x74, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x07, 0x72, 0x65, 0x64, 0x44, 0x6f, 0x74, 0x73, 0x22, 0x60, 0x0a, 0x13, 0x4d, 0x6f, 0x64,
	0x69, 0x66, 0x79, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71,
	0x12, 0x1b, 0x0a, 0x09, 0x6e, 0x69, 0x63, 0x6b, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x6e, 0x69, 0x63, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a,
	0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x61,
	0x76, 0x61, 0x74, 0x61, 0x72, 0x12, 0x14, 0x0a, 0x05, 0x66, 0x72, 0x61, 0x6d, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x66, 0x72, 0x61, 0x6d, 0x65, 0x22, 0x37, 0x0a, 0x13, 0x4d,
	0x6f, 0x64, 0x69, 0x66, 0x79, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52,
	0x73, 0x70, 0x12, 0x20, 0x0a, 0x03, 0x72, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x0e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52,
	0x03, 0x72, 0x65, 0x74, 0x22, 0x29, 0x0a, 0x10, 0x43, 0x44, 0x4b, 0x65, 0x79, 0x45, 0x78, 0x63,
	0x68, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x65, 0x71, 0x12, 0x15, 0x0a, 0x06, 0x63, 0x64, 0x5f, 0x6b,
	0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x63, 0x64, 0x4b, 0x65, 0x79, 0x22,
	0x65, 0x0a, 0x10, 0x43, 0x44, 0x4b, 0x65, 0x79, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65,
	0x52, 0x73, 0x70, 0x12, 0x20, 0x0a, 0x03, 0x72, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x0e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x52, 0x03, 0x72, 0x65, 0x74, 0x12, 0x2f, 0x0a, 0x0b, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f,
	0x69, 0x6e, 0x66, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x52, 0x0a, 0x72, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x42, 0x3d, 0x5a, 0x3b, 0x67, 0x69, 0x74, 0x2e, 0x6b, 0x65,
	0x65, 0x70, 0x66, 0x61, 0x6e, 0x63, 0x79, 0x2e, 0x78, 0x79, 0x7a, 0x2f, 0x62, 0x61, 0x63, 0x6b,
	0x2d, 0x65, 0x6e, 0x64, 0x2f, 0x66, 0x61, 0x6e, 0x63, 0x79, 0x2d, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2f, 0x70, 0x6b, 0x67, 0x2f, 0x63, 0x70, 0x62, 0x2f, 0x68, 0x61, 0x6c, 0x6c, 0x3b, 0x68,
	0x61, 0x6c, 0x6c, 0x50, 0x42, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_hall_proto_rawDescOnce sync.Once
	file_hall_proto_rawDescData = file_hall_proto_rawDesc
)

func file_hall_proto_rawDescGZIP() []byte {
	file_hall_proto_rawDescOnce.Do(func() {
		file_hall_proto_rawDescData = protoimpl.X.CompressGZIP(file_hall_proto_rawDescData)
	})
	return file_hall_proto_rawDescData
}

var file_hall_proto_msgTypes = make([]protoimpl.MessageInfo, 101)
var file_hall_proto_goTypes = []interface{}{
	(*GetRoomInfoReq)(nil),                 // 0: hallPB.GetRoomInfoReq
	(*GetRoomInfoRsp)(nil),                 // 1: hallPB.GetRoomInfoRsp
	(*GetLastGameInfoReq)(nil),             // 2: hallPB.GetLastGameInfoReq
	(*GetLastGameInfoRsp)(nil),             // 3: hallPB.GetLastGameInfoRsp
	(*EnterFisheryReq)(nil),                // 4: hallPB.EnterFisheryReq
	(*EnterFisheryRsp)(nil),                // 5: hallPB.EnterFisheryRsp
	(*GetItemInfoReq)(nil),                 // 6: hallPB.GetItemInfoReq
	(*GetItemInfoRsp)(nil),                 // 7: hallPB.GetItemInfoRsp
	(*GetItemInfoByTypeReq)(nil),           // 8: hallPB.GetItemInfoByTypeReq
	(*GetItemInfoByTypeRsp)(nil),           // 9: hallPB.GetItemInfoByTypeRsp
	(*UpdateItemInfoNtf)(nil),              // 10: hallPB.UpdateItemInfoNtf
	(*GetGoodsBuyInfoReq)(nil),             // 11: hallPB.GetGoodsBuyInfoReq
	(*GetGoodsBuyInfoRsp)(nil),             // 12: hallPB.GetGoodsBuyInfoRsp
	(*StoreBuyReq)(nil),                    // 13: hallPB.StoreBuyReq
	(*StoreBuyRsp)(nil),                    // 14: hallPB.StoreBuyRsp
	(*ExpLevelChangeNtf)(nil),              // 15: hallPB.ExpLevelChangeNtf
	(*GetPlayerInfoReq)(nil),               // 16: hallPB.GetPlayerInfoReq
	(*GetPlayerInfoRsp)(nil),               // 17: hallPB.GetPlayerInfoRsp
	(*UseItemReq)(nil),                     // 18: hallPB.UseItemReq
	(*UseItemRsp)(nil),                     // 19: hallPB.UseItemRsp
	(*FirstEnterHallReq)(nil),              // 20: hallPB.FirstEnterHallReq
	(*FirstEnterHallRsp)(nil),              // 21: hallPB.FirstEnterHallRsp
	(*StoreMultiBuyReq)(nil),               // 22: hallPB.StoreMultiBuyReq
	(*StoreMultiBuyRsp)(nil),               // 23: hallPB.StoreMultiBuyRsp
	(*GetStatListReq)(nil),                 // 24: hallPB.GetStatListReq
	(*GetStatListRsp)(nil),                 // 25: hallPB.GetStatListRsp
	(*StatInfoUpdateNtf)(nil),              // 26: hallPB.StatInfoUpdateNtf
	(*GetRodRigInfoReq)(nil),               // 27: hallPB.GetRodRigInfoReq
	(*GetRodRigInfoRsp)(nil),               // 28: hallPB.GetRodRigInfoRsp
	(*UpdateRodRigInfoReq)(nil),            // 29: hallPB.UpdateRodRigInfoReq
	(*UpdateRodRigInfoRsp)(nil),            // 30: hallPB.UpdateRodRigInfoRsp
	(*DeleteRodRigInfoReq)(nil),            // 31: hallPB.DeleteRodRigInfoReq
	(*DeleteRodRigInfoRsp)(nil),            // 32: hallPB.DeleteRodRigInfoRsp
	(*UpdateRodRigNtf)(nil),                // 33: hallPB.UpdateRodRigNtf
	(*ModifyDurabilityReq)(nil),            // 34: hallPB.ModifyDurabilityReq
	(*ModifyDurabilityRsp)(nil),            // 35: hallPB.ModifyDurabilityRsp
	(*MaintainRodItemReq)(nil),             // 36: hallPB.MaintainRodItemReq
	(*MaintainRodItemRsp)(nil),             // 37: hallPB.MaintainRodItemRsp
	(*MaintainStorageItemReq)(nil),         // 38: hallPB.MaintainStorageItemReq
	(*MaintainStorageItemRsp)(nil),         // 39: hallPB.MaintainStorageItemRsp
	(*SellItemReq)(nil),                    // 40: hallPB.SellItemReq
	(*SellItemRsp)(nil),                    // 41: hallPB.SellItemRsp
	(*GetTripRodReq)(nil),                  // 42: hallPB.GetTripRodReq
	(*GetTripRodRsp)(nil),                  // 43: hallPB.GetTripRodRsp
	(*LoadTripRodReq)(nil),                 // 44: hallPB.LoadTripRodReq
	(*LoadTripRodRsp)(nil),                 // 45: hallPB.LoadTripRodRsp
	(*SaveTripRodReq)(nil),                 // 46: hallPB.SaveTripRodReq
	(*SaveTripRodRsp)(nil),                 // 47: hallPB.SaveTripRodRsp
	(*SaveNewTripRodReq)(nil),              // 48: hallPB.SaveNewTripRodReq
	(*SaveNewTripRodRsp)(nil),              // 49: hallPB.SaveNewTripRodRsp
	(*PutTripRodReq)(nil),                  // 50: hallPB.PutTripRodReq
	(*PutTripRodRsp)(nil),                  // 51: hallPB.PutTripRodRsp
	(*DelTripRodReq)(nil),                  // 52: hallPB.DelTripRodReq
	(*DelTripRodRsp)(nil),                  // 53: hallPB.DelTripRodRsp
	(*SplitTripRodReq)(nil),                // 54: hallPB.SplitTripRodReq
	(*SplitTripRodRsp)(nil),                // 55: hallPB.SplitTripRodRsp
	(*UpdateTripRodReq)(nil),               // 56: hallPB.UpdateTripRodReq
	(*UpdateTripRodRsp)(nil),               // 57: hallPB.UpdateTripRodRsp
	(*BatchUpdateTripRodReq)(nil),          // 58: hallPB.BatchUpdateTripRodReq
	(*BatchUpdateTripRodRsp)(nil),          // 59: hallPB.BatchUpdateTripRodRsp
	(*GetTripBagReq)(nil),                  // 60: hallPB.GetTripBagReq
	(*GetTripBagRsp)(nil),                  // 61: hallPB.GetTripBagRsp
	(*ModifyTripBagReq)(nil),               // 62: hallPB.ModifyTripBagReq
	(*ModifyTripBagRsp)(nil),               // 63: hallPB.ModifyTripBagRsp
	(*TripBagQuickBuyReq)(nil),             // 64: hallPB.TripBagQuickBuyReq
	(*TripBagQuickBuyRsp)(nil),             // 65: hallPB.TripBagQuickBuyRsp
	(*TripBagUseReq)(nil),                  // 66: hallPB.TripBagUseReq
	(*TripBagUseRsp)(nil),                  // 67: hallPB.TripBagUseRsp
	(*CheckForbidWordReq)(nil),             // 68: hallPB.CheckForbidWordReq
	(*CheckForbidWordRsp)(nil),             // 69: hallPB.CheckForbidWordRsp
	(*UnloadAllTripBagReq)(nil),            // 70: hallPB.UnloadAllTripBagReq
	(*UnloadAllTripBagRsp)(nil),            // 71: hallPB.UnloadAllTripBagRsp
	(*ItemHeapReq)(nil),                    // 72: hallPB.ItemHeapReq
	(*ItemHeapRsp)(nil),                    // 73: hallPB.ItemHeapRsp
	(*ItemHeapUpdateNotify)(nil),           // 74: hallPB.ItemHeapUpdateNotify
	(*RealNameAuthReq)(nil),                // 75: hallPB.RealNameAuthReq
	(*RealNameAuthRsp)(nil),                // 76: hallPB.RealNameAuthRsp
	(*AntiAddictionNtf)(nil),               // 77: hallPB.AntiAddictionNtf
	(*TempCashBuyReq)(nil),                 // 78: hallPB.TempCashBuyReq
	(*TempCashBuyRsp)(nil),                 // 79: hallPB.TempCashBuyRsp
	(*UpdateGuideProgressReq)(nil),         // 80: hallPB.UpdateGuideProgressReq
	(*UpdateGuideProgressRsp)(nil),         // 81: hallPB.UpdateGuideProgressRsp
	(*ContinuousLoginReq)(nil),             // 82: hallPB.ContinuousLoginReq
	(*ContinuousLoginRsp)(nil),             // 83: hallPB.ContinuousLoginRsp
	(*ContinuousLoginRewardReq)(nil),       // 84: hallPB.ContinuousLoginRewardReq
	(*ContinuousLoginRewardRsp)(nil),       // 85: hallPB.ContinuousLoginRewardRsp
	(*RedDotInfo)(nil),                     // 86: hallPB.RedDotInfo
	(*ClearRedDotRequest)(nil),             // 87: hallPB.ClearRedDotRequest
	(*ClearRedDotResponse)(nil),            // 88: hallPB.ClearRedDotResponse
	(*GetPlayerAllRedDotsRequest)(nil),     // 89: hallPB.GetPlayerAllRedDotsRequest
	(*GetPlayerAllRedDotsResponse)(nil),    // 90: hallPB.GetPlayerAllRedDotsResponse
	(*GetPlayerModuleRedDotsRequest)(nil),  // 91: hallPB.GetPlayerModuleRedDotsRequest
	(*GetPlayerModuleRedDotsResponse)(nil), // 92: hallPB.GetPlayerModuleRedDotsResponse
	(*RedDotUpdateNotify)(nil),             // 93: hallPB.RedDotUpdateNotify
	(*ModifyPlayerInfoReq)(nil),            // 94: hallPB.ModifyPlayerInfoReq
	(*ModifyPlayerInfoRsp)(nil),            // 95: hallPB.ModifyPlayerInfoRsp
	(*CDKeyExchangeReq)(nil),               // 96: hallPB.CDKeyExchangeReq
	(*CDKeyExchangeRsp)(nil),               // 97: hallPB.CDKeyExchangeRsp
	nil,                                    // 98: hallPB.ModifyDurabilityReq.ChangeEntry
	nil,                                    // 99: hallPB.ItemHeapRsp.ItemHeapsEntry
	nil,                                    // 100: hallPB.ItemHeapUpdateNotify.ItemHeapsEntry
	(*common.Result)(nil),                  // 101: common.Result
	(*common.RoomInfo)(nil),                // 102: common.RoomInfo
	(common.GAME_TYPE)(0),                  // 103: common.GAME_TYPE
	(common.ROOM_TYPE)(0),                  // 104: common.ROOM_TYPE
	(*common.ItemInfo)(nil),                // 105: common.ItemInfo
	(common.ITEM_TYPE)(0),                  // 106: common.ITEM_TYPE
	(*common.Reward)(nil),                  // 107: common.Reward
	(common.STORAGE_TYPE)(0),               // 108: common.STORAGE_TYPE
	(*common.GoodsBuyInfo)(nil),            // 109: common.GoodsBuyInfo
	(common.STORE_SHOW_STYLE)(0),           // 110: common.STORE_SHOW_STYLE
	(*common.ExpLevelChangeInfo)(nil),      // 111: common.ExpLevelChangeInfo
	(*common.PlayerBaseInfo)(nil),          // 112: common.PlayerBaseInfo
	(*common.ItemBase)(nil),                // 113: common.ItemBase
	(common.ITEM_SOURCE_TYPE)(0),           // 114: common.ITEM_SOURCE_TYPE
	(*common.AnnPopupInfo)(nil),            // 115: common.AnnPopupInfo
	(*common.StatInfo)(nil),                // 116: common.StatInfo
	(*common.RodRigInfo)(nil),              // 117: common.RodRigInfo
	(*common.RodBagInfo)(nil),              // 118: common.RodBagInfo
	(common.TRIP_BAG_TYPE)(0),              // 119: common.TRIP_BAG_TYPE
	(common.TRIP_BAG_OPERATE)(0),           // 120: common.TRIP_BAG_OPERATE
	(common.USER_MODULE_TYPE)(0),           // 121: common.USER_MODULE_TYPE
}
var file_hall_proto_depIdxs = []int32{
	101, // 0: hallPB.GetRoomInfoRsp.ret:type_name -> common.Result
	102, // 1: hallPB.GetRoomInfoRsp.room_info:type_name -> common.RoomInfo
	101, // 2: hallPB.GetLastGameInfoRsp.ret:type_name -> common.Result
	102, // 3: hallPB.GetLastGameInfoRsp.last_game:type_name -> common.RoomInfo
	103, // 4: hallPB.EnterFisheryReq.game_type:type_name -> common.GAME_TYPE
	104, // 5: hallPB.EnterFisheryReq.room_type:type_name -> common.ROOM_TYPE
	101, // 6: hallPB.EnterFisheryRsp.ret:type_name -> common.Result
	102, // 7: hallPB.EnterFisheryRsp.room_info:type_name -> common.RoomInfo
	101, // 8: hallPB.GetItemInfoRsp.ret:type_name -> common.Result
	105, // 9: hallPB.GetItemInfoRsp.item_info:type_name -> common.ItemInfo
	106, // 10: hallPB.GetItemInfoByTypeReq.item_type:type_name -> common.ITEM_TYPE
	101, // 11: hallPB.GetItemInfoByTypeRsp.ret:type_name -> common.Result
	105, // 12: hallPB.GetItemInfoByTypeRsp.item_info:type_name -> common.ItemInfo
	107, // 13: hallPB.UpdateItemInfoNtf.reward_info:type_name -> common.Reward
	108, // 14: hallPB.UpdateItemInfoNtf.storage:type_name -> common.STORAGE_TYPE
	101, // 15: hallPB.GetGoodsBuyInfoRsp.ret:type_name -> common.Result
	109, // 16: hallPB.GetGoodsBuyInfoRsp.buy_info:type_name -> common.GoodsBuyInfo
	110, // 17: hallPB.StoreBuyReq.style_type:type_name -> common.STORE_SHOW_STYLE
	101, // 18: hallPB.StoreBuyRsp.ret:type_name -> common.Result
	107, // 19: hallPB.StoreBuyRsp.reward_info:type_name -> common.Reward
	111, // 20: hallPB.ExpLevelChangeNtf.level_change_info:type_name -> common.ExpLevelChangeInfo
	112, // 21: hallPB.GetPlayerInfoRsp.player_info:type_name -> common.PlayerBaseInfo
	113, // 22: hallPB.UseItemReq.item_info:type_name -> common.ItemBase
	114, // 23: hallPB.UseItemReq.src_type:type_name -> common.ITEM_SOURCE_TYPE
	108, // 24: hallPB.UseItemReq.storage:type_name -> common.STORAGE_TYPE
	101, // 25: hallPB.UseItemRsp.ret:type_name -> common.Result
	112, // 26: hallPB.FirstEnterHallRsp.player_info:type_name -> common.PlayerBaseInfo
	115, // 27: hallPB.FirstEnterHallRsp.popup_info:type_name -> common.AnnPopupInfo
	113, // 28: hallPB.StoreMultiBuyReq.goods_list:type_name -> common.ItemBase
	110, // 29: hallPB.StoreMultiBuyReq.style_type:type_name -> common.STORE_SHOW_STYLE
	101, // 30: hallPB.StoreMultiBuyRsp.ret:type_name -> common.Result
	113, // 31: hallPB.StoreMultiBuyRsp.goos_list:type_name -> common.ItemBase
	107, // 32: hallPB.StoreMultiBuyRsp.reward_info:type_name -> common.Reward
	101, // 33: hallPB.GetStatListRsp.ret:type_name -> common.Result
	116, // 34: hallPB.GetStatListRsp.list:type_name -> common.StatInfo
	116, // 35: hallPB.StatInfoUpdateNtf.list:type_name -> common.StatInfo
	101, // 36: hallPB.GetRodRigInfoRsp.ret:type_name -> common.Result
	117, // 37: hallPB.GetRodRigInfoRsp.rig_list:type_name -> common.RodRigInfo
	117, // 38: hallPB.UpdateRodRigInfoReq.rig_info:type_name -> common.RodRigInfo
	101, // 39: hallPB.UpdateRodRigInfoRsp.ret:type_name -> common.Result
	117, // 40: hallPB.UpdateRodRigInfoRsp.rig_info:type_name -> common.RodRigInfo
	101, // 41: hallPB.DeleteRodRigInfoRsp.ret:type_name -> common.Result
	118, // 42: hallPB.UpdateRodRigNtf.rod_info:type_name -> common.RodBagInfo
	98,  // 43: hallPB.ModifyDurabilityReq.change:type_name -> hallPB.ModifyDurabilityReq.ChangeEntry
	101, // 44: hallPB.ModifyDurabilityRsp.ret:type_name -> common.Result
	118, // 45: hallPB.ModifyDurabilityRsp.rod_info:type_name -> common.RodBagInfo
	101, // 46: hallPB.MaintainRodItemRsp.ret:type_name -> common.Result
	118, // 47: hallPB.MaintainRodItemRsp.rod_info:type_name -> common.RodBagInfo
	107, // 48: hallPB.MaintainRodItemRsp.cost:type_name -> common.Reward
	108, // 49: hallPB.MaintainStorageItemReq.store_type:type_name -> common.STORAGE_TYPE
	101, // 50: hallPB.MaintainStorageItemRsp.ret:type_name -> common.Result
	107, // 51: hallPB.MaintainStorageItemRsp.cost:type_name -> common.Reward
	108, // 52: hallPB.SellItemReq.store_type:type_name -> common.STORAGE_TYPE
	113, // 53: hallPB.SellItemReq.list:type_name -> common.ItemBase
	101, // 54: hallPB.SellItemRsp.ret:type_name -> common.Result
	107, // 55: hallPB.SellItemRsp.reward:type_name -> common.Reward
	108, // 56: hallPB.GetTripRodReq.storage:type_name -> common.STORAGE_TYPE
	101, // 57: hallPB.GetTripRodRsp.ret:type_name -> common.Result
	118, // 58: hallPB.GetTripRodRsp.list:type_name -> common.RodBagInfo
	101, // 59: hallPB.LoadTripRodRsp.ret:type_name -> common.Result
	118, // 60: hallPB.LoadTripRodRsp.info:type_name -> common.RodBagInfo
	101, // 61: hallPB.SaveTripRodRsp.ret:type_name -> common.Result
	118, // 62: hallPB.SaveTripRodRsp.info:type_name -> common.RodBagInfo
	56,  // 63: hallPB.SaveNewTripRodReq.list:type_name -> hallPB.UpdateTripRodReq
	101, // 64: hallPB.SaveNewTripRodRsp.ret:type_name -> common.Result
	118, // 65: hallPB.SaveNewTripRodRsp.info:type_name -> common.RodBagInfo
	101, // 66: hallPB.PutTripRodRsp.ret:type_name -> common.Result
	118, // 67: hallPB.PutTripRodRsp.info:type_name -> common.RodBagInfo
	101, // 68: hallPB.DelTripRodRsp.ret:type_name -> common.Result
	118, // 69: hallPB.DelTripRodRsp.info:type_name -> common.RodBagInfo
	101, // 70: hallPB.SplitTripRodRsp.ret:type_name -> common.Result
	108, // 71: hallPB.UpdateTripRodReq.storage:type_name -> common.STORAGE_TYPE
	101, // 72: hallPB.UpdateTripRodRsp.ret:type_name -> common.Result
	118, // 73: hallPB.UpdateTripRodRsp.info:type_name -> common.RodBagInfo
	56,  // 74: hallPB.BatchUpdateTripRodReq.list:type_name -> hallPB.UpdateTripRodReq
	101, // 75: hallPB.BatchUpdateTripRodRsp.ret:type_name -> common.Result
	118, // 76: hallPB.BatchUpdateTripRodRsp.info:type_name -> common.RodBagInfo
	119, // 77: hallPB.GetTripBagReq.type:type_name -> common.TRIP_BAG_TYPE
	101, // 78: hallPB.GetTripBagRsp.ret:type_name -> common.Result
	119, // 79: hallPB.GetTripBagRsp.type:type_name -> common.TRIP_BAG_TYPE
	105, // 80: hallPB.GetTripBagRsp.list:type_name -> common.ItemInfo
	119, // 81: hallPB.ModifyTripBagReq.type:type_name -> common.TRIP_BAG_TYPE
	120, // 82: hallPB.ModifyTripBagReq.operate:type_name -> common.TRIP_BAG_OPERATE
	101, // 83: hallPB.ModifyTripBagRsp.ret:type_name -> common.Result
	119, // 84: hallPB.ModifyTripBagRsp.type:type_name -> common.TRIP_BAG_TYPE
	120, // 85: hallPB.ModifyTripBagRsp.operate:type_name -> common.TRIP_BAG_OPERATE
	101, // 86: hallPB.TripBagQuickBuyRsp.ret:type_name -> common.Result
	119, // 87: hallPB.TripBagUseReq.type:type_name -> common.TRIP_BAG_TYPE
	101, // 88: hallPB.TripBagUseRsp.ret:type_name -> common.Result
	119, // 89: hallPB.TripBagUseRsp.type:type_name -> common.TRIP_BAG_TYPE
	101, // 90: hallPB.UnloadAllTripBagRsp.ret:type_name -> common.Result
	101, // 91: hallPB.ItemHeapRsp.ret:type_name -> common.Result
	99,  // 92: hallPB.ItemHeapRsp.item_heaps:type_name -> hallPB.ItemHeapRsp.ItemHeapsEntry
	100, // 93: hallPB.ItemHeapUpdateNotify.item_heaps:type_name -> hallPB.ItemHeapUpdateNotify.ItemHeapsEntry
	101, // 94: hallPB.RealNameAuthRsp.ret:type_name -> common.Result
	101, // 95: hallPB.TempCashBuyRsp.ret:type_name -> common.Result
	107, // 96: hallPB.TempCashBuyRsp.reward_info:type_name -> common.Reward
	101, // 97: hallPB.UpdateGuideProgressRsp.ret:type_name -> common.Result
	101, // 98: hallPB.ContinuousLoginRsp.ret:type_name -> common.Result
	101, // 99: hallPB.ContinuousLoginRewardRsp.ret:type_name -> common.Result
	107, // 100: hallPB.ContinuousLoginRewardRsp.reward:type_name -> common.Reward
	121, // 101: hallPB.RedDotInfo.module_type:type_name -> common.USER_MODULE_TYPE
	121, // 102: hallPB.ClearRedDotRequest.module_type:type_name -> common.USER_MODULE_TYPE
	101, // 103: hallPB.ClearRedDotResponse.ret:type_name -> common.Result
	86,  // 104: hallPB.ClearRedDotResponse.red_dot:type_name -> hallPB.RedDotInfo
	101, // 105: hallPB.GetPlayerAllRedDotsResponse.ret:type_name -> common.Result
	86,  // 106: hallPB.GetPlayerAllRedDotsResponse.red_dots:type_name -> hallPB.RedDotInfo
	121, // 107: hallPB.GetPlayerModuleRedDotsRequest.module_type:type_name -> common.USER_MODULE_TYPE
	101, // 108: hallPB.GetPlayerModuleRedDotsResponse.ret:type_name -> common.Result
	86,  // 109: hallPB.GetPlayerModuleRedDotsResponse.red_dots:type_name -> hallPB.RedDotInfo
	86,  // 110: hallPB.RedDotUpdateNotify.red_dots:type_name -> hallPB.RedDotInfo
	101, // 111: hallPB.ModifyPlayerInfoRsp.ret:type_name -> common.Result
	101, // 112: hallPB.CDKeyExchangeRsp.ret:type_name -> common.Result
	107, // 113: hallPB.CDKeyExchangeRsp.reward_info:type_name -> common.Reward
	114, // [114:114] is the sub-list for method output_type
	114, // [114:114] is the sub-list for method input_type
	114, // [114:114] is the sub-list for extension type_name
	114, // [114:114] is the sub-list for extension extendee
	0,   // [0:114] is the sub-list for field type_name
}

func init() { file_hall_proto_init() }
func file_hall_proto_init() {
	if File_hall_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_hall_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRoomInfoReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRoomInfoRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLastGameInfoReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLastGameInfoRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EnterFisheryReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EnterFisheryRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetItemInfoReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetItemInfoRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetItemInfoByTypeReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetItemInfoByTypeRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateItemInfoNtf); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetGoodsBuyInfoReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetGoodsBuyInfoRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StoreBuyReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StoreBuyRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExpLevelChangeNtf); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPlayerInfoReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPlayerInfoRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UseItemReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UseItemRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FirstEnterHallReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FirstEnterHallRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StoreMultiBuyReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StoreMultiBuyRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetStatListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetStatListRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StatInfoUpdateNtf); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRodRigInfoReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRodRigInfoRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateRodRigInfoReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateRodRigInfoRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteRodRigInfoReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteRodRigInfoRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateRodRigNtf); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModifyDurabilityReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModifyDurabilityRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MaintainRodItemReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MaintainRodItemRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MaintainStorageItemReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[39].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MaintainStorageItemRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[40].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SellItemReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[41].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SellItemRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[42].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTripRodReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[43].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTripRodRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[44].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LoadTripRodReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[45].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LoadTripRodRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[46].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SaveTripRodReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[47].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SaveTripRodRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[48].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SaveNewTripRodReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[49].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SaveNewTripRodRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[50].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PutTripRodReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[51].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PutTripRodRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[52].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DelTripRodReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[53].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DelTripRodRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[54].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SplitTripRodReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[55].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SplitTripRodRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[56].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateTripRodReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[57].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateTripRodRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[58].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchUpdateTripRodReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[59].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchUpdateTripRodRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[60].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTripBagReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[61].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTripBagRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[62].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModifyTripBagReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[63].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModifyTripBagRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[64].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TripBagQuickBuyReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[65].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TripBagQuickBuyRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[66].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TripBagUseReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[67].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TripBagUseRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[68].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckForbidWordReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[69].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckForbidWordRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[70].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UnloadAllTripBagReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[71].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UnloadAllTripBagRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[72].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ItemHeapReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[73].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ItemHeapRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[74].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ItemHeapUpdateNotify); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[75].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RealNameAuthReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[76].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RealNameAuthRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[77].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AntiAddictionNtf); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[78].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TempCashBuyReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[79].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TempCashBuyRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[80].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateGuideProgressReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[81].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateGuideProgressRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[82].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ContinuousLoginReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[83].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ContinuousLoginRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[84].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ContinuousLoginRewardReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[85].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ContinuousLoginRewardRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[86].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RedDotInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[87].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ClearRedDotRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[88].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ClearRedDotResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[89].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPlayerAllRedDotsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[90].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPlayerAllRedDotsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[91].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPlayerModuleRedDotsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[92].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPlayerModuleRedDotsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[93].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RedDotUpdateNotify); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[94].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModifyPlayerInfoReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[95].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModifyPlayerInfoRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[96].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CDKeyExchangeReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_hall_proto_msgTypes[97].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CDKeyExchangeRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_hall_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   101,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_hall_proto_goTypes,
		DependencyIndexes: file_hall_proto_depIdxs,
		MessageInfos:      file_hall_proto_msgTypes,
	}.Build()
	File_hall_proto = out.File
	file_hall_proto_rawDesc = nil
	file_hall_proto_goTypes = nil
	file_hall_proto_depIdxs = nil
}
