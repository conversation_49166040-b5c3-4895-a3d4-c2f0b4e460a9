// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/fancy-common/pkg/repo/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type HookConst struct {
	Id               int64 `json:"id"`
	ReqFishInterval  int64 `json:"reqFishInterval"`
	EmptyFishWgReset int64 `json:"emptyFishWgReset"`
}

var lockHookConst sync.RWMutex
var storeHookConst sync.Map
var strHookConst string = "hook_const"

func InitHookConstCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strHookConst, watchHookConstFunc)
	return LoadAllHookConstCfg()
}

func fixKeyHookConst(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strHookConst)
}
func watchHookConstFunc(key string, js string) {
	store, ok := storeHookConst.Load(key)
	if !ok {
		store = &HookConst{}
	}
	errUnmarshal := json.Unmarshal([]byte(js), &store)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeHookConst.Store(key, store)
}

func GetHookConst(option ...consulconfig.Option) *HookConst {
	fitKey := fixKeyHookConst(option...)
	store, ok := storeHookConst.Load(fitKey)
	if ok {
		tblHookConst, ok := store.(*HookConst)
		if ok {
			return tblHookConst
		}
	}
	lockHookConst.Lock()
	defer lockHookConst.Unlock()
	store, ok = storeHookConst.Load(fitKey)
	if ok {
		tblHookConst, ok := store.(*HookConst)
		if ok {
			return tblHookConst
		}
	}
	tblHookConst := &HookConst{}
	hook_const_str, err := consulconfig.GetInstance().GetConfig(strHookConst, option...)
	if hook_const_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(hook_const_str), &tblHookConst)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v, json:%s", strHookConst, errUnmarshal, hook_const_str)
		return nil
	}
	storeHookConst.Store(fitKey, tblHookConst)
	return tblHookConst
}

func LoadAllHookConstCfg() error {
	var successChannels []int32
	for _, channel := range consulconfig.GetAllChannelMap() {
		_, err := consulconfig.GetInstance().GetConfig(strHookConst, consulconfig.WithChannel(channel))
		if err != nil {
			continue
		}
		successChannels = append(successChannels, channel)
	}
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "HookConst", successChannels)
	return nil
}
