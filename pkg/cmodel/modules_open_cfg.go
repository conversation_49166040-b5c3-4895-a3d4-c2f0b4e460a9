// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/fancy-common/pkg/repo/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type ModulesOpen struct {
	Id          int64  `json:"id"`
	UnlockLevel int32  `json:"unlockLevel"`
	ModuleType  int32  `json:"moduleType"`
	Open        int32  `json:"open"`
	ModuleName  int32  `json:"moduleName"`
	Icon        string `json:"icon"`
	Mark        string `json:"mark"`
}

var lockModulesOpen sync.RWMutex
var storeModulesOpen sync.Map
var strModulesOpen string = "modules_open"

func InitModulesOpenCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strModulesOpen, watchModulesOpenFunc)
	return LoadAllModulesOpenCfg()
}

func fixKeyModulesOpen(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strModulesOpen)
}
func watchModulesOpenFunc(key string, js string) {
	mapModulesOpen := make(map[int64]*ModulesOpen)
	errUnmarshal := json.Unmarshal([]byte(js), &mapModulesOpen)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeModulesOpen.Store(key, mapModulesOpen)
}

func GetAllModulesOpen(option ...consulconfig.Option) map[int64]*ModulesOpen {
	fitKey := fixKeyModulesOpen(option...)
	store, ok := storeModulesOpen.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*ModulesOpen)
		if ok {
			return storeMap
		}
	}
	lockModulesOpen.Lock()
	defer lockModulesOpen.Unlock()
	store, ok = storeModulesOpen.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*ModulesOpen)
		if ok {
			return storeMap
		}
	}
	tblModulesOpen := make(map[int64]*ModulesOpen)
	modules_open_str, err := consulconfig.GetInstance().GetConfig(strModulesOpen, option...)
	if modules_open_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(modules_open_str), &tblModulesOpen)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "modules_open", errUnmarshal)
		return nil
	}
	storeModulesOpen.Store(fitKey, tblModulesOpen)
	return tblModulesOpen
}

func GetModulesOpen(id int64, option ...consulconfig.Option) *ModulesOpen {
	fitKey := fixKeyModulesOpen(option...)
	store, ok := storeModulesOpen.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*ModulesOpen)
		if ok {
			return storeMap[id]
		}
	}
	lockModulesOpen.Lock()
	defer lockModulesOpen.Unlock()
	store, ok = storeModulesOpen.Load(fitKey)
	if ok {
		tblModulesOpen, ok := store.(*ModulesOpen)
		if ok {
			return tblModulesOpen
		}
	}
	tblModulesOpen := make(map[int64]*ModulesOpen)
	modules_open_str, err := consulconfig.GetInstance().GetConfig(strModulesOpen, option...)
	if modules_open_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(modules_open_str), &tblModulesOpen)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "modules_open", errUnmarshal)
		return nil
	}
	storeModulesOpen.Store(fitKey, tblModulesOpen)
	return tblModulesOpen[id]
}

func LoadAllModulesOpenCfg() error {
	var successChannels []int32
	for _, channel := range consulconfig.GetAllChannelMap() {
		_, err := consulconfig.GetInstance().GetConfig(strModulesOpen, consulconfig.WithChannel(channel))
		if err != nil {
			continue
		}
		successChannels = append(successChannels, channel)
	}
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "ModulesOpen", successChannels)
	return nil
}
