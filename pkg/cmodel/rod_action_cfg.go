// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/fancy-common/pkg/repo/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type RodAction struct {
	Id             int32     `json:"id"`
	Desc           string    `json:"desc"`
	ActionType     int32     `json:"actionType"`
	Segments       []float32 `json:"segments"`
	Weights        []float32 `json:"weights"`
	Constants      []float32 `json:"constants"`
	CurveFactor    float32   `json:"curveFactor"`
	FrictionFactor []float32 `json:"frictionFactor"`
	HooksetCircle  int32     `json:"hooksetCircle"`
}

var lockRodAction sync.RWMutex
var storeRodAction sync.Map
var strRodAction string = "rod_action"

func InitRodActionCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strRodAction, watchRodActionFunc)
	return LoadAllRodActionCfg()
}

func fixKeyRodAction(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strRodAction)
}
func watchRodActionFunc(key string, js string) {
	mapRodAction := make(map[int64]*RodAction)
	errUnmarshal := json.Unmarshal([]byte(js), &mapRodAction)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeRodAction.Store(key, mapRodAction)
}

func GetAllRodAction(option ...consulconfig.Option) map[int64]*RodAction {
	fitKey := fixKeyRodAction(option...)
	store, ok := storeRodAction.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*RodAction)
		if ok {
			return storeMap
		}
	}
	lockRodAction.Lock()
	defer lockRodAction.Unlock()
	store, ok = storeRodAction.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*RodAction)
		if ok {
			return storeMap
		}
	}
	tblRodAction := make(map[int64]*RodAction)
	rod_action_str, err := consulconfig.GetInstance().GetConfig(strRodAction, option...)
	if rod_action_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(rod_action_str), &tblRodAction)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "rod_action", errUnmarshal)
		return nil
	}
	storeRodAction.Store(fitKey, tblRodAction)
	return tblRodAction
}

func GetRodAction(id int64, option ...consulconfig.Option) *RodAction {
	fitKey := fixKeyRodAction(option...)
	store, ok := storeRodAction.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*RodAction)
		if ok {
			return storeMap[id]
		}
	}
	lockRodAction.Lock()
	defer lockRodAction.Unlock()
	store, ok = storeRodAction.Load(fitKey)
	if ok {
		tblRodAction, ok := store.(*RodAction)
		if ok {
			return tblRodAction
		}
	}
	tblRodAction := make(map[int64]*RodAction)
	rod_action_str, err := consulconfig.GetInstance().GetConfig(strRodAction, option...)
	if rod_action_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(rod_action_str), &tblRodAction)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "rod_action", errUnmarshal)
		return nil
	}
	storeRodAction.Store(fitKey, tblRodAction)
	return tblRodAction[id]
}

func LoadAllRodActionCfg() error {
	var successChannels []int32
	for _, channel := range consulconfig.GetAllChannelMap() {
		_, err := consulconfig.GetInstance().GetConfig(strRodAction, consulconfig.WithChannel(channel))
		if err != nil {
			continue
		}
		successChannels = append(successChannels, channel)
	}
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "RodAction", successChannels)
	return nil
}
