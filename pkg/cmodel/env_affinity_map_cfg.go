// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/fancy-common/pkg/repo/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type EnvAffinityMap struct {
	Id   int64  `json:"id"`
	Name string `json:"name"`
	Mark string `json:"mark"`
}

var lockEnvAffinityMap sync.RWMutex
var storeEnvAffinityMap sync.Map
var strEnvAffinityMap string = "env_affinity_map"

func InitEnvAffinityMapCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strEnvAffinityMap, watchEnvAffinityMapFunc)
	return LoadAllEnvAffinityMapCfg()
}

func fixKeyEnvAffinityMap(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strEnvAffinityMap)
}
func watchEnvAffinityMapFunc(key string, js string) {
	mapEnvAffinityMap := make(map[int64]*EnvAffinityMap)
	errUnmarshal := json.Unmarshal([]byte(js), &mapEnvAffinityMap)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeEnvAffinityMap.Store(key, mapEnvAffinityMap)
}

func GetAllEnvAffinityMap(option ...consulconfig.Option) map[int64]*EnvAffinityMap {
	fitKey := fixKeyEnvAffinityMap(option...)
	store, ok := storeEnvAffinityMap.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*EnvAffinityMap)
		if ok {
			return storeMap
		}
	}
	lockEnvAffinityMap.Lock()
	defer lockEnvAffinityMap.Unlock()
	store, ok = storeEnvAffinityMap.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*EnvAffinityMap)
		if ok {
			return storeMap
		}
	}
	tblEnvAffinityMap := make(map[int64]*EnvAffinityMap)
	env_affinity_map_str, err := consulconfig.GetInstance().GetConfig(strEnvAffinityMap, option...)
	if env_affinity_map_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(env_affinity_map_str), &tblEnvAffinityMap)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "env_affinity_map", errUnmarshal)
		return nil
	}
	storeEnvAffinityMap.Store(fitKey, tblEnvAffinityMap)
	return tblEnvAffinityMap
}

func GetEnvAffinityMap(id int64, option ...consulconfig.Option) *EnvAffinityMap {
	fitKey := fixKeyEnvAffinityMap(option...)
	store, ok := storeEnvAffinityMap.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*EnvAffinityMap)
		if ok {
			return storeMap[id]
		}
	}
	lockEnvAffinityMap.Lock()
	defer lockEnvAffinityMap.Unlock()
	store, ok = storeEnvAffinityMap.Load(fitKey)
	if ok {
		tblEnvAffinityMap, ok := store.(*EnvAffinityMap)
		if ok {
			return tblEnvAffinityMap
		}
	}
	tblEnvAffinityMap := make(map[int64]*EnvAffinityMap)
	env_affinity_map_str, err := consulconfig.GetInstance().GetConfig(strEnvAffinityMap, option...)
	if env_affinity_map_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(env_affinity_map_str), &tblEnvAffinityMap)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "env_affinity_map", errUnmarshal)
		return nil
	}
	storeEnvAffinityMap.Store(fitKey, tblEnvAffinityMap)
	return tblEnvAffinityMap[id]
}

func LoadAllEnvAffinityMapCfg() error {
	var successChannels []int32
	for _, channel := range consulconfig.GetAllChannelMap() {
		_, err := consulconfig.GetInstance().GetConfig(strEnvAffinityMap, consulconfig.WithChannel(channel))
		if err != nil {
			continue
		}
		successChannels = append(successChannels, channel)
	}
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "EnvAffinityMap", successChannels)
	return nil
}
