// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/fancy-common/pkg/repo/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type LanguageStoreLangName struct {
	Chinesesimplified string `json:"chinesesimplified"`
	English           string `json:"english"`
	Test              string `json:"test"`
}

type LanguageStoreLangDes struct {
	Chinesesimplified string `json:"chinesesimplified"`
	English           string `json:"english"`
	Test              string `json:"test"`
}

type LanguageStore struct {
	Id       int64                 `json:"id"`
	Name     string                `json:"name"`
	LangName LanguageStoreLangName `json:"lang_name"`
	LangDes  LanguageStoreLangDes  `json:"lang_des"`
}

var lockLanguageStore sync.RWMutex
var storeLanguageStore sync.Map
var strLanguageStore string = "language_store"

func InitLanguageStoreCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strLanguageStore, watchLanguageStoreFunc)
	return LoadAllLanguageStoreCfg()
}

func fixKeyLanguageStore(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strLanguageStore)
}
func watchLanguageStoreFunc(key string, js string) {
	mapLanguageStore := make(map[int64]*LanguageStore)
	errUnmarshal := json.Unmarshal([]byte(js), &mapLanguageStore)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeLanguageStore.Store(key, mapLanguageStore)
}

func GetAllLanguageStore(option ...consulconfig.Option) map[int64]*LanguageStore {
	fitKey := fixKeyLanguageStore(option...)
	store, ok := storeLanguageStore.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*LanguageStore)
		if ok {
			return storeMap
		}
	}
	lockLanguageStore.Lock()
	defer lockLanguageStore.Unlock()
	store, ok = storeLanguageStore.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*LanguageStore)
		if ok {
			return storeMap
		}
	}
	tblLanguageStore := make(map[int64]*LanguageStore)
	language_store_str, err := consulconfig.GetInstance().GetConfig(strLanguageStore, option...)
	if language_store_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(language_store_str), &tblLanguageStore)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "language_store", errUnmarshal)
		return nil
	}
	storeLanguageStore.Store(fitKey, tblLanguageStore)
	return tblLanguageStore
}

func GetLanguageStore(id int64, option ...consulconfig.Option) *LanguageStore {
	fitKey := fixKeyLanguageStore(option...)
	store, ok := storeLanguageStore.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*LanguageStore)
		if ok {
			return storeMap[id]
		}
	}
	lockLanguageStore.Lock()
	defer lockLanguageStore.Unlock()
	store, ok = storeLanguageStore.Load(fitKey)
	if ok {
		tblLanguageStore, ok := store.(*LanguageStore)
		if ok {
			return tblLanguageStore
		}
	}
	tblLanguageStore := make(map[int64]*LanguageStore)
	language_store_str, err := consulconfig.GetInstance().GetConfig(strLanguageStore, option...)
	if language_store_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(language_store_str), &tblLanguageStore)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "language_store", errUnmarshal)
		return nil
	}
	storeLanguageStore.Store(fitKey, tblLanguageStore)
	return tblLanguageStore[id]
}

func LoadAllLanguageStoreCfg() error {
	var successChannels []int32
	for _, channel := range consulconfig.GetAllChannelMap() {
		_, err := consulconfig.GetInstance().GetConfig(strLanguageStore, consulconfig.WithChannel(channel))
		if err != nil {
			continue
		}
		successChannels = append(successChannels, channel)
	}
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "LanguageStore", successChannels)
	return nil
}
