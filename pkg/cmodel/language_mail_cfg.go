// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/fancy-common/pkg/repo/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type LanguageMail struct {
	Id                       int64  `json:"id"`
	Name                     string `json:"name"`
	Chinesesimplifiedtitle   string `json:"chinesesimplifiedtitle"`
	Chinesesimplifiedcontent string `json:"chinesesimplifiedcontent"`
	Englishtitle             string `json:"englishtitle"`
	Englishcontent           string `json:"englishcontent"`
}

var lockLanguageMail sync.RWMutex
var storeLanguageMail sync.Map
var strLanguageMail string = "language_mail"

func InitLanguageMailCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strLanguageMail, watchLanguageMailFunc)
	return LoadAllLanguageMailCfg()
}

func fixKeyLanguageMail(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strLanguageMail)
}
func watchLanguageMailFunc(key string, js string) {
	mapLanguageMail := make(map[int64]*LanguageMail)
	errUnmarshal := json.Unmarshal([]byte(js), &mapLanguageMail)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeLanguageMail.Store(key, mapLanguageMail)
}

func GetAllLanguageMail(option ...consulconfig.Option) map[int64]*LanguageMail {
	fitKey := fixKeyLanguageMail(option...)
	store, ok := storeLanguageMail.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*LanguageMail)
		if ok {
			return storeMap
		}
	}
	lockLanguageMail.Lock()
	defer lockLanguageMail.Unlock()
	store, ok = storeLanguageMail.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*LanguageMail)
		if ok {
			return storeMap
		}
	}
	tblLanguageMail := make(map[int64]*LanguageMail)
	language_mail_str, err := consulconfig.GetInstance().GetConfig(strLanguageMail, option...)
	if language_mail_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(language_mail_str), &tblLanguageMail)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "language_mail", errUnmarshal)
		return nil
	}
	storeLanguageMail.Store(fitKey, tblLanguageMail)
	return tblLanguageMail
}

func GetLanguageMail(id int64, option ...consulconfig.Option) *LanguageMail {
	fitKey := fixKeyLanguageMail(option...)
	store, ok := storeLanguageMail.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*LanguageMail)
		if ok {
			return storeMap[id]
		}
	}
	lockLanguageMail.Lock()
	defer lockLanguageMail.Unlock()
	store, ok = storeLanguageMail.Load(fitKey)
	if ok {
		tblLanguageMail, ok := store.(*LanguageMail)
		if ok {
			return tblLanguageMail
		}
	}
	tblLanguageMail := make(map[int64]*LanguageMail)
	language_mail_str, err := consulconfig.GetInstance().GetConfig(strLanguageMail, option...)
	if language_mail_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(language_mail_str), &tblLanguageMail)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "language_mail", errUnmarshal)
		return nil
	}
	storeLanguageMail.Store(fitKey, tblLanguageMail)
	return tblLanguageMail[id]
}

func LoadAllLanguageMailCfg() error {
	var successChannels []int32
	for _, channel := range consulconfig.GetAllChannelMap() {
		_, err := consulconfig.GetInstance().GetConfig(strLanguageMail, consulconfig.WithChannel(channel))
		if err != nil {
			continue
		}
		successChannels = append(successChannels, channel)
	}
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "LanguageMail", successChannels)
	return nil
}
