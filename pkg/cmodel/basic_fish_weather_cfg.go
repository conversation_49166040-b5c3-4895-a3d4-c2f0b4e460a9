// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/fancy-common/pkg/repo/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type BasicFishWeather struct {
	Id          int64  `json:"id"`
	Name        string `json:"name"`
	WeatherType int32  `json:"weatherType"`
	Description string `json:"description"`
}

var lockBasicFishWeather sync.RWMutex
var storeBasicFishWeather sync.Map
var strBasicFishWeather string = "basic_fish_weather"

func InitBasicFishWeatherCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strBasicFishWeather, watchBasicFishWeatherFunc)
	return LoadAllBasicFishWeatherCfg()
}

func fixKeyBasicFishWeather(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strBasicFishWeather)
}
func watchBasicFishWeatherFunc(key string, js string) {
	mapBasicFishWeather := make(map[int64]*BasicFishWeather)
	errUnmarshal := json.Unmarshal([]byte(js), &mapBasicFishWeather)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeBasicFishWeather.Store(key, mapBasicFishWeather)
}

func GetAllBasicFishWeather(option ...consulconfig.Option) map[int64]*BasicFishWeather {
	fitKey := fixKeyBasicFishWeather(option...)
	store, ok := storeBasicFishWeather.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*BasicFishWeather)
		if ok {
			return storeMap
		}
	}
	lockBasicFishWeather.Lock()
	defer lockBasicFishWeather.Unlock()
	store, ok = storeBasicFishWeather.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*BasicFishWeather)
		if ok {
			return storeMap
		}
	}
	tblBasicFishWeather := make(map[int64]*BasicFishWeather)
	basic_fish_weather_str, err := consulconfig.GetInstance().GetConfig(strBasicFishWeather, option...)
	if basic_fish_weather_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(basic_fish_weather_str), &tblBasicFishWeather)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "basic_fish_weather", errUnmarshal)
		return nil
	}
	storeBasicFishWeather.Store(fitKey, tblBasicFishWeather)
	return tblBasicFishWeather
}

func GetBasicFishWeather(id int64, option ...consulconfig.Option) *BasicFishWeather {
	fitKey := fixKeyBasicFishWeather(option...)
	store, ok := storeBasicFishWeather.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*BasicFishWeather)
		if ok {
			return storeMap[id]
		}
	}
	lockBasicFishWeather.Lock()
	defer lockBasicFishWeather.Unlock()
	store, ok = storeBasicFishWeather.Load(fitKey)
	if ok {
		tblBasicFishWeather, ok := store.(*BasicFishWeather)
		if ok {
			return tblBasicFishWeather
		}
	}
	tblBasicFishWeather := make(map[int64]*BasicFishWeather)
	basic_fish_weather_str, err := consulconfig.GetInstance().GetConfig(strBasicFishWeather, option...)
	if basic_fish_weather_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(basic_fish_weather_str), &tblBasicFishWeather)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "basic_fish_weather", errUnmarshal)
		return nil
	}
	storeBasicFishWeather.Store(fitKey, tblBasicFishWeather)
	return tblBasicFishWeather[id]
}

func LoadAllBasicFishWeatherCfg() error {
	var successChannels []int32
	for _, channel := range consulconfig.GetAllChannelMap() {
		_, err := consulconfig.GetInstance().GetConfig(strBasicFishWeather, consulconfig.WithChannel(channel))
		if err != nil {
			continue
		}
		successChannels = append(successChannels, channel)
	}
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "BasicFishWeather", successChannels)
	return nil
}
