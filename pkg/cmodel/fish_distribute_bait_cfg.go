// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/fancy-common/pkg/repo/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type FishDistributeBaitBait struct {
	BaitId int64 `json:"baitId"`
	Weight int32 `json:"weight"`
}

type FishDistributeBait struct {
	Id   int64                    `json:"id"`
	Name string                   `json:"name"`
	Bait []FishDistributeBaitBait `json:"bait"`
}

var lockFishDistributeBait sync.RWMutex
var storeFishDistributeBait sync.Map
var strFishDistributeBait string = "fish_distribute_bait"

func InitFishDistributeBaitCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strFishDistributeBait, watchFishDistributeBaitFunc)
	return LoadAllFishDistributeBaitCfg()
}

func fixKeyFishDistributeBait(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strFishDistributeBait)
}
func watchFishDistributeBaitFunc(key string, js string) {
	mapFishDistributeBait := make(map[int64]*FishDistributeBait)
	errUnmarshal := json.Unmarshal([]byte(js), &mapFishDistributeBait)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeFishDistributeBait.Store(key, mapFishDistributeBait)
}

func GetAllFishDistributeBait(option ...consulconfig.Option) map[int64]*FishDistributeBait {
	fitKey := fixKeyFishDistributeBait(option...)
	store, ok := storeFishDistributeBait.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*FishDistributeBait)
		if ok {
			return storeMap
		}
	}
	lockFishDistributeBait.Lock()
	defer lockFishDistributeBait.Unlock()
	store, ok = storeFishDistributeBait.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*FishDistributeBait)
		if ok {
			return storeMap
		}
	}
	tblFishDistributeBait := make(map[int64]*FishDistributeBait)
	fish_distribute_bait_str, err := consulconfig.GetInstance().GetConfig(strFishDistributeBait, option...)
	if fish_distribute_bait_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(fish_distribute_bait_str), &tblFishDistributeBait)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "fish_distribute_bait", errUnmarshal)
		return nil
	}
	storeFishDistributeBait.Store(fitKey, tblFishDistributeBait)
	return tblFishDistributeBait
}

func GetFishDistributeBait(id int64, option ...consulconfig.Option) *FishDistributeBait {
	fitKey := fixKeyFishDistributeBait(option...)
	store, ok := storeFishDistributeBait.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*FishDistributeBait)
		if ok {
			return storeMap[id]
		}
	}
	lockFishDistributeBait.Lock()
	defer lockFishDistributeBait.Unlock()
	store, ok = storeFishDistributeBait.Load(fitKey)
	if ok {
		tblFishDistributeBait, ok := store.(*FishDistributeBait)
		if ok {
			return tblFishDistributeBait
		}
	}
	tblFishDistributeBait := make(map[int64]*FishDistributeBait)
	fish_distribute_bait_str, err := consulconfig.GetInstance().GetConfig(strFishDistributeBait, option...)
	if fish_distribute_bait_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(fish_distribute_bait_str), &tblFishDistributeBait)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "fish_distribute_bait", errUnmarshal)
		return nil
	}
	storeFishDistributeBait.Store(fitKey, tblFishDistributeBait)
	return tblFishDistributeBait[id]
}

func LoadAllFishDistributeBaitCfg() error {
	var successChannels []int32
	for _, channel := range consulconfig.GetAllChannelMap() {
		_, err := consulconfig.GetInstance().GetConfig(strFishDistributeBait, consulconfig.WithChannel(channel))
		if err != nil {
			continue
		}
		successChannels = append(successChannels, channel)
	}
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "FishDistributeBait", successChannels)
	return nil
}
