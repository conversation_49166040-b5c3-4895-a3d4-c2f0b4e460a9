// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/fancy-common/pkg/repo/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type RigRule struct {
	Id         int64   `json:"id"`
	Name       string  `json:"name"`
	Mark       string  `json:"mark"`
	PlayType   int32   `json:"playType"`
	RodType    []int32 `json:"rodType"`
	ReelType   []int32 `json:"reelType"`
	LineType   []int32 `json:"lineType"`
	LeaderType []int32 `json:"leaderType"`
	BaitType   []int32 `json:"baitType"`
	HookType   []int32 `json:"hookType"`
	FloatType  []int32 `json:"floatType"`
}

var lockRigRule sync.RWMutex
var storeRigRule sync.Map
var strRigRule string = "rig_rule"

func InitRigRuleCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strRigRule, watchRigRuleFunc)
	return LoadAllRigRuleCfg()
}

func fixKeyRigRule(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strRigRule)
}
func watchRigRuleFunc(key string, js string) {
	mapRigRule := make(map[int64]*RigRule)
	errUnmarshal := json.Unmarshal([]byte(js), &mapRigRule)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeRigRule.Store(key, mapRigRule)
}

func GetAllRigRule(option ...consulconfig.Option) map[int64]*RigRule {
	fitKey := fixKeyRigRule(option...)
	store, ok := storeRigRule.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*RigRule)
		if ok {
			return storeMap
		}
	}
	lockRigRule.Lock()
	defer lockRigRule.Unlock()
	store, ok = storeRigRule.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*RigRule)
		if ok {
			return storeMap
		}
	}
	tblRigRule := make(map[int64]*RigRule)
	rig_rule_str, err := consulconfig.GetInstance().GetConfig(strRigRule, option...)
	if rig_rule_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(rig_rule_str), &tblRigRule)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "rig_rule", errUnmarshal)
		return nil
	}
	storeRigRule.Store(fitKey, tblRigRule)
	return tblRigRule
}

func GetRigRule(id int64, option ...consulconfig.Option) *RigRule {
	fitKey := fixKeyRigRule(option...)
	store, ok := storeRigRule.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*RigRule)
		if ok {
			return storeMap[id]
		}
	}
	lockRigRule.Lock()
	defer lockRigRule.Unlock()
	store, ok = storeRigRule.Load(fitKey)
	if ok {
		tblRigRule, ok := store.(*RigRule)
		if ok {
			return tblRigRule
		}
	}
	tblRigRule := make(map[int64]*RigRule)
	rig_rule_str, err := consulconfig.GetInstance().GetConfig(strRigRule, option...)
	if rig_rule_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(rig_rule_str), &tblRigRule)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "rig_rule", errUnmarshal)
		return nil
	}
	storeRigRule.Store(fitKey, tblRigRule)
	return tblRigRule[id]
}

func LoadAllRigRuleCfg() error {
	var successChannels []int32
	for _, channel := range consulconfig.GetAllChannelMap() {
		_, err := consulconfig.GetInstance().GetConfig(strRigRule, consulconfig.WithChannel(channel))
		if err != nil {
			continue
		}
		successChannels = append(successChannels, channel)
	}
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "RigRule", successChannels)
	return nil
}
