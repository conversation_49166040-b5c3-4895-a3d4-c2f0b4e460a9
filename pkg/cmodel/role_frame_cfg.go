// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/fancy-common/pkg/repo/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type RoleFrame struct {
	Id   int32  `json:"id"`
	Name string `json:"name"`
	Mark string `json:"mark"`
}

var lockRoleFrame sync.RWMutex
var storeRoleFrame sync.Map
var strRoleFrame string = "role_frame"

func InitRoleFrameCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strRoleFrame, watchRoleFrameFunc)
	return LoadAllRoleFrameCfg()
}

func fixKeyRoleFrame(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strRoleFrame)
}
func watchRoleFrameFunc(key string, js string) {
	mapRoleFrame := make(map[int64]*RoleFrame)
	errUnmarshal := json.Unmarshal([]byte(js), &mapRoleFrame)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeRoleFrame.Store(key, mapRoleFrame)
}

func GetAllRoleFrame(option ...consulconfig.Option) map[int64]*RoleFrame {
	fitKey := fixKeyRoleFrame(option...)
	store, ok := storeRoleFrame.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*RoleFrame)
		if ok {
			return storeMap
		}
	}
	lockRoleFrame.Lock()
	defer lockRoleFrame.Unlock()
	store, ok = storeRoleFrame.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*RoleFrame)
		if ok {
			return storeMap
		}
	}
	tblRoleFrame := make(map[int64]*RoleFrame)
	role_frame_str, err := consulconfig.GetInstance().GetConfig(strRoleFrame, option...)
	if role_frame_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(role_frame_str), &tblRoleFrame)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "role_frame", errUnmarshal)
		return nil
	}
	storeRoleFrame.Store(fitKey, tblRoleFrame)
	return tblRoleFrame
}

func GetRoleFrame(id int64, option ...consulconfig.Option) *RoleFrame {
	fitKey := fixKeyRoleFrame(option...)
	store, ok := storeRoleFrame.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*RoleFrame)
		if ok {
			return storeMap[id]
		}
	}
	lockRoleFrame.Lock()
	defer lockRoleFrame.Unlock()
	store, ok = storeRoleFrame.Load(fitKey)
	if ok {
		tblRoleFrame, ok := store.(*RoleFrame)
		if ok {
			return tblRoleFrame
		}
	}
	tblRoleFrame := make(map[int64]*RoleFrame)
	role_frame_str, err := consulconfig.GetInstance().GetConfig(strRoleFrame, option...)
	if role_frame_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(role_frame_str), &tblRoleFrame)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "role_frame", errUnmarshal)
		return nil
	}
	storeRoleFrame.Store(fitKey, tblRoleFrame)
	return tblRoleFrame[id]
}

func LoadAllRoleFrameCfg() error {
	var successChannels []int32
	for _, channel := range consulconfig.GetAllChannelMap() {
		_, err := consulconfig.GetInstance().GetConfig(strRoleFrame, consulconfig.WithChannel(channel))
		if err != nil {
			continue
		}
		successChannels = append(successChannels, channel)
	}
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "RoleFrame", successChannels)
	return nil
}
