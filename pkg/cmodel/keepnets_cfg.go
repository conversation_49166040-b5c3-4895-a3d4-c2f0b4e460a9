// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/fancy-common/pkg/repo/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type Keepnets struct {
	Id            int64  `json:"id"`
	SubType       int32  `json:"subType"`
	Name          string `json:"name"`
	Mark          string `json:"mark"`
	ArtId         string `json:"artId"`
	Durability    int32  `json:"durability"`
	MaxFishWeight int32  `json:"maxFishWeight"`
	MaxCapacity   int32  `json:"maxCapacity"`
}

var lockKeepnets sync.RWMutex
var storeKeepnets sync.Map
var strKeepnets string = "keepnets"

func InitKeepnetsCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strKeepnets, watchKeepnetsFunc)
	return LoadAllKeepnetsCfg()
}

func fixKeyKeepnets(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strKeepnets)
}
func watchKeepnetsFunc(key string, js string) {
	mapKeepnets := make(map[int64]*Keepnets)
	errUnmarshal := json.Unmarshal([]byte(js), &mapKeepnets)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeKeepnets.Store(key, mapKeepnets)
}

func GetAllKeepnets(option ...consulconfig.Option) map[int64]*Keepnets {
	fitKey := fixKeyKeepnets(option...)
	store, ok := storeKeepnets.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*Keepnets)
		if ok {
			return storeMap
		}
	}
	lockKeepnets.Lock()
	defer lockKeepnets.Unlock()
	store, ok = storeKeepnets.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*Keepnets)
		if ok {
			return storeMap
		}
	}
	tblKeepnets := make(map[int64]*Keepnets)
	keepnets_str, err := consulconfig.GetInstance().GetConfig(strKeepnets, option...)
	if keepnets_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(keepnets_str), &tblKeepnets)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "keepnets", errUnmarshal)
		return nil
	}
	storeKeepnets.Store(fitKey, tblKeepnets)
	return tblKeepnets
}

func GetKeepnets(id int64, option ...consulconfig.Option) *Keepnets {
	fitKey := fixKeyKeepnets(option...)
	store, ok := storeKeepnets.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*Keepnets)
		if ok {
			return storeMap[id]
		}
	}
	lockKeepnets.Lock()
	defer lockKeepnets.Unlock()
	store, ok = storeKeepnets.Load(fitKey)
	if ok {
		tblKeepnets, ok := store.(*Keepnets)
		if ok {
			return tblKeepnets
		}
	}
	tblKeepnets := make(map[int64]*Keepnets)
	keepnets_str, err := consulconfig.GetInstance().GetConfig(strKeepnets, option...)
	if keepnets_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(keepnets_str), &tblKeepnets)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "keepnets", errUnmarshal)
		return nil
	}
	storeKeepnets.Store(fitKey, tblKeepnets)
	return tblKeepnets[id]
}

func LoadAllKeepnetsCfg() error {
	var successChannels []int32
	for _, channel := range consulconfig.GetAllChannelMap() {
		_, err := consulconfig.GetInstance().GetConfig(strKeepnets, consulconfig.WithChannel(channel))
		if err != nil {
			continue
		}
		successChannels = append(successChannels, channel)
	}
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "Keepnets", successChannels)
	return nil
}
