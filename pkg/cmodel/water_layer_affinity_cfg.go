// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/fancy-common/pkg/repo/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type WaterLayerAffinityList struct {
	LayerType int32   `json:"layerType"`
	Coeff     float64 `json:"coeff"`
}

type WaterLayerAffinity struct {
	Id   int64                    `json:"id"`
	Name string                   `json:"name"`
	List []WaterLayerAffinityList `json:"List"`
	Mark string                   `json:"mark"`
}

var lockWaterLayerAffinity sync.RWMutex
var storeWaterLayerAffinity sync.Map
var strWaterLayerAffinity string = "water_layer_affinity"

func InitWaterLayerAffinityCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strWaterLayerAffinity, watchWaterLayerAffinityFunc)
	return LoadAllWaterLayerAffinityCfg()
}

func fixKeyWaterLayerAffinity(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strWaterLayerAffinity)
}
func watchWaterLayerAffinityFunc(key string, js string) {
	mapWaterLayerAffinity := make(map[int64]*WaterLayerAffinity)
	errUnmarshal := json.Unmarshal([]byte(js), &mapWaterLayerAffinity)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeWaterLayerAffinity.Store(key, mapWaterLayerAffinity)
}

func GetAllWaterLayerAffinity(option ...consulconfig.Option) map[int64]*WaterLayerAffinity {
	fitKey := fixKeyWaterLayerAffinity(option...)
	store, ok := storeWaterLayerAffinity.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*WaterLayerAffinity)
		if ok {
			return storeMap
		}
	}
	lockWaterLayerAffinity.Lock()
	defer lockWaterLayerAffinity.Unlock()
	store, ok = storeWaterLayerAffinity.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*WaterLayerAffinity)
		if ok {
			return storeMap
		}
	}
	tblWaterLayerAffinity := make(map[int64]*WaterLayerAffinity)
	water_layer_affinity_str, err := consulconfig.GetInstance().GetConfig(strWaterLayerAffinity, option...)
	if water_layer_affinity_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(water_layer_affinity_str), &tblWaterLayerAffinity)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "water_layer_affinity", errUnmarshal)
		return nil
	}
	storeWaterLayerAffinity.Store(fitKey, tblWaterLayerAffinity)
	return tblWaterLayerAffinity
}

func GetWaterLayerAffinity(id int64, option ...consulconfig.Option) *WaterLayerAffinity {
	fitKey := fixKeyWaterLayerAffinity(option...)
	store, ok := storeWaterLayerAffinity.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*WaterLayerAffinity)
		if ok {
			return storeMap[id]
		}
	}
	lockWaterLayerAffinity.Lock()
	defer lockWaterLayerAffinity.Unlock()
	store, ok = storeWaterLayerAffinity.Load(fitKey)
	if ok {
		tblWaterLayerAffinity, ok := store.(*WaterLayerAffinity)
		if ok {
			return tblWaterLayerAffinity
		}
	}
	tblWaterLayerAffinity := make(map[int64]*WaterLayerAffinity)
	water_layer_affinity_str, err := consulconfig.GetInstance().GetConfig(strWaterLayerAffinity, option...)
	if water_layer_affinity_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(water_layer_affinity_str), &tblWaterLayerAffinity)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "water_layer_affinity", errUnmarshal)
		return nil
	}
	storeWaterLayerAffinity.Store(fitKey, tblWaterLayerAffinity)
	return tblWaterLayerAffinity[id]
}

func LoadAllWaterLayerAffinityCfg() error {
	var successChannels []int32
	for _, channel := range consulconfig.GetAllChannelMap() {
		_, err := consulconfig.GetInstance().GetConfig(strWaterLayerAffinity, consulconfig.WithChannel(channel))
		if err != nil {
			continue
		}
		successChannels = append(successChannels, channel)
	}
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "WaterLayerAffinity", successChannels)
	return nil
}
