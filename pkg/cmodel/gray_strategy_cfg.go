// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/fancy-common/pkg/repo/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type GrayStrategy struct {
	Enable    bool   `json:"enable"`
	RuleType  int64  `json:"ruleType"`
	IpList    string `json:"ipList"`
	Version   string `json:"version"`
	UidTail   string `json:"uidTail"`
	TagList   string `json:"tagList"`
	StartTime string `json:"startTime"`
	EndTime   string `json:"endTime"`
}

var lockGrayStrategy sync.RWMutex
var storeGrayStrategy sync.Map
var strGrayStrategy string = "gray_strategy"

func InitGrayStrategyCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strGrayStrategy, watchGrayStrategyFunc)
	return LoadAllGrayStrategyCfg()
}

func fixKeyGrayStrategy(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strGrayStrategy)
}
func watchGrayStrategyFunc(key string, js string) {
	store, ok := storeGrayStrategy.Load(key)
	if !ok {
		store = &GrayStrategy{}
	}
	errUnmarshal := json.Unmarshal([]byte(js), &store)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeGrayStrategy.Store(key, store)
}

func GetGrayStrategy(option ...consulconfig.Option) *GrayStrategy {
	fitKey := fixKeyGrayStrategy(option...)
	store, ok := storeGrayStrategy.Load(fitKey)
	if ok {
		tblGrayStrategy, ok := store.(*GrayStrategy)
		if ok {
			return tblGrayStrategy
		}
	}
	lockGrayStrategy.Lock()
	defer lockGrayStrategy.Unlock()
	store, ok = storeGrayStrategy.Load(fitKey)
	if ok {
		tblGrayStrategy, ok := store.(*GrayStrategy)
		if ok {
			return tblGrayStrategy
		}
	}
	tblGrayStrategy := &GrayStrategy{}
	gray_strategy_str, err := consulconfig.GetInstance().GetConfig(strGrayStrategy, option...)
	if gray_strategy_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(gray_strategy_str), &tblGrayStrategy)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v, json:%s", strGrayStrategy, errUnmarshal, gray_strategy_str)
		return nil
	}
	storeGrayStrategy.Store(fitKey, tblGrayStrategy)
	return tblGrayStrategy
}

func LoadAllGrayStrategyCfg() error {
	var successChannels []int32
	for _, channel := range consulconfig.GetAllChannelMap() {
		_, err := consulconfig.GetInstance().GetConfig(strGrayStrategy, consulconfig.WithChannel(channel))
		if err != nil {
			continue
		}
		successChannels = append(successChannels, channel)
	}
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "GrayStrategy", successChannels)
	return nil
}
