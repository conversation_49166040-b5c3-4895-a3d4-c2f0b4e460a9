// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/fancy-common/pkg/repo/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type WhiteList struct {
	Id        int64  `json:"id"`
	Name      string `json:"name"`
	WhiteType int32  `json:"whiteType"`
	Uid       int64  `json:"uid"`
	Mark      string `json:"mark"`
}

var lockWhiteList sync.RWMutex
var storeWhiteList sync.Map
var strWhiteList string = "white_list"

func InitWhiteListCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strWhiteList, watchWhiteListFunc)
	return LoadAllWhiteListCfg()
}

func fixKeyWhiteList(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strWhiteList)
}
func watchWhiteListFunc(key string, js string) {
	mapWhiteList := make(map[int64]*WhiteList)
	errUnmarshal := json.Unmarshal([]byte(js), &mapWhiteList)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeWhiteList.Store(key, mapWhiteList)
}

func GetAllWhiteList(option ...consulconfig.Option) map[int64]*WhiteList {
	fitKey := fixKeyWhiteList(option...)
	store, ok := storeWhiteList.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*WhiteList)
		if ok {
			return storeMap
		}
	}
	lockWhiteList.Lock()
	defer lockWhiteList.Unlock()
	store, ok = storeWhiteList.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*WhiteList)
		if ok {
			return storeMap
		}
	}
	tblWhiteList := make(map[int64]*WhiteList)
	white_list_str, err := consulconfig.GetInstance().GetConfig(strWhiteList, option...)
	if white_list_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(white_list_str), &tblWhiteList)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "white_list", errUnmarshal)
		return nil
	}
	storeWhiteList.Store(fitKey, tblWhiteList)
	return tblWhiteList
}

func GetWhiteList(id int64, option ...consulconfig.Option) *WhiteList {
	fitKey := fixKeyWhiteList(option...)
	store, ok := storeWhiteList.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*WhiteList)
		if ok {
			return storeMap[id]
		}
	}
	lockWhiteList.Lock()
	defer lockWhiteList.Unlock()
	store, ok = storeWhiteList.Load(fitKey)
	if ok {
		tblWhiteList, ok := store.(*WhiteList)
		if ok {
			return tblWhiteList
		}
	}
	tblWhiteList := make(map[int64]*WhiteList)
	white_list_str, err := consulconfig.GetInstance().GetConfig(strWhiteList, option...)
	if white_list_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(white_list_str), &tblWhiteList)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "white_list", errUnmarshal)
		return nil
	}
	storeWhiteList.Store(fitKey, tblWhiteList)
	return tblWhiteList[id]
}

func LoadAllWhiteListCfg() error {
	var successChannels []int32
	for _, channel := range consulconfig.GetAllChannelMap() {
		_, err := consulconfig.GetInstance().GetConfig(strWhiteList, consulconfig.WithChannel(channel))
		if err != nil {
			continue
		}
		successChannels = append(successChannels, channel)
	}
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "WhiteList", successChannels)
	return nil
}
