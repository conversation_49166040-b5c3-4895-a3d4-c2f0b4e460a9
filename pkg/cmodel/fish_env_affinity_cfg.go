// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/fancy-common/pkg/repo/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type FishEnvAffinity struct {
	Id                    int64   `json:"id"`
	Name                  string  `json:"name"`
	FishQuality           int64   `json:"fishQuality"`
	StructId              int64   `json:"structId"`
	TempId                int64   `json:"tempId"`
	LayerId               int64   `json:"layerId"`
	LightId               int64   `json:"lightId"`
	BaitCoeffGroup        int64   `json:"baitCoeffGroup"`
	BaitTypeCoeffGroup    int64   `json:"baitTypeCoeffGroup"`
	PeriodCoeffGroup      int64   `json:"periodCoeffGroup"`
	PressureSensitivity   float64 `json:"pressureSensitivity"`
	MinAdaptLureRatio     float64 `json:"minAdaptLureRatio"`
	MaxAdaptLureRatio     float64 `json:"maxAdaptLureRatio"`
	UnderLengthDecayCoeff float64 `json:"underLengthDecayCoeff"`
	OverLengthDecayCoeff  float64 `json:"overLengthDecayCoeff"`
	MaxAcceptLengthRatio  float64 `json:"maxAcceptLengthRatio"`
	Mark                  string  `json:"mark"`
}

var lockFishEnvAffinity sync.RWMutex
var storeFishEnvAffinity sync.Map
var strFishEnvAffinity string = "fish_env_affinity"

func InitFishEnvAffinityCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strFishEnvAffinity, watchFishEnvAffinityFunc)
	return LoadAllFishEnvAffinityCfg()
}

func fixKeyFishEnvAffinity(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strFishEnvAffinity)
}
func watchFishEnvAffinityFunc(key string, js string) {
	mapFishEnvAffinity := make(map[int64]*FishEnvAffinity)
	errUnmarshal := json.Unmarshal([]byte(js), &mapFishEnvAffinity)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeFishEnvAffinity.Store(key, mapFishEnvAffinity)
}

func GetAllFishEnvAffinity(option ...consulconfig.Option) map[int64]*FishEnvAffinity {
	fitKey := fixKeyFishEnvAffinity(option...)
	store, ok := storeFishEnvAffinity.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*FishEnvAffinity)
		if ok {
			return storeMap
		}
	}
	lockFishEnvAffinity.Lock()
	defer lockFishEnvAffinity.Unlock()
	store, ok = storeFishEnvAffinity.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*FishEnvAffinity)
		if ok {
			return storeMap
		}
	}
	tblFishEnvAffinity := make(map[int64]*FishEnvAffinity)
	fish_env_affinity_str, err := consulconfig.GetInstance().GetConfig(strFishEnvAffinity, option...)
	if fish_env_affinity_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(fish_env_affinity_str), &tblFishEnvAffinity)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "fish_env_affinity", errUnmarshal)
		return nil
	}
	storeFishEnvAffinity.Store(fitKey, tblFishEnvAffinity)
	return tblFishEnvAffinity
}

func GetFishEnvAffinity(id int64, option ...consulconfig.Option) *FishEnvAffinity {
	fitKey := fixKeyFishEnvAffinity(option...)
	store, ok := storeFishEnvAffinity.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*FishEnvAffinity)
		if ok {
			return storeMap[id]
		}
	}
	lockFishEnvAffinity.Lock()
	defer lockFishEnvAffinity.Unlock()
	store, ok = storeFishEnvAffinity.Load(fitKey)
	if ok {
		tblFishEnvAffinity, ok := store.(*FishEnvAffinity)
		if ok {
			return tblFishEnvAffinity
		}
	}
	tblFishEnvAffinity := make(map[int64]*FishEnvAffinity)
	fish_env_affinity_str, err := consulconfig.GetInstance().GetConfig(strFishEnvAffinity, option...)
	if fish_env_affinity_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(fish_env_affinity_str), &tblFishEnvAffinity)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "fish_env_affinity", errUnmarshal)
		return nil
	}
	storeFishEnvAffinity.Store(fitKey, tblFishEnvAffinity)
	return tblFishEnvAffinity[id]
}

func LoadAllFishEnvAffinityCfg() error {
	var successChannels []int32
	for _, channel := range consulconfig.GetAllChannelMap() {
		_, err := consulconfig.GetInstance().GetConfig(strFishEnvAffinity, consulconfig.WithChannel(channel))
		if err != nil {
			continue
		}
		successChannels = append(successChannels, channel)
	}
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "FishEnvAffinity", successChannels)
	return nil
}
