// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/fancy-common/pkg/repo/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type RandomName struct {
	Id                int64  `json:"id"`
	Chinesesimplified string `json:"chinesesimplified"`
	English           string `json:"english"`
	Test              string `json:"test"`
}

var lockRandomName sync.RWMutex
var storeRandomName sync.Map
var strRandomName string = "random_name"

func InitRandomNameCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strRandomName, watchRandomNameFunc)
	return LoadAllRandomNameCfg()
}

func fixKeyRandomName(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strRandomName)
}
func watchRandomNameFunc(key string, js string) {
	mapRandomName := make(map[int64]*RandomName)
	errUnmarshal := json.Unmarshal([]byte(js), &mapRandomName)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeRandomName.Store(key, mapRandomName)
}

func GetAllRandomName(option ...consulconfig.Option) map[int64]*RandomName {
	fitKey := fixKeyRandomName(option...)
	store, ok := storeRandomName.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*RandomName)
		if ok {
			return storeMap
		}
	}
	lockRandomName.Lock()
	defer lockRandomName.Unlock()
	store, ok = storeRandomName.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*RandomName)
		if ok {
			return storeMap
		}
	}
	tblRandomName := make(map[int64]*RandomName)
	random_name_str, err := consulconfig.GetInstance().GetConfig(strRandomName, option...)
	if random_name_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(random_name_str), &tblRandomName)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "random_name", errUnmarshal)
		return nil
	}
	storeRandomName.Store(fitKey, tblRandomName)
	return tblRandomName
}

func GetRandomName(id int64, option ...consulconfig.Option) *RandomName {
	fitKey := fixKeyRandomName(option...)
	store, ok := storeRandomName.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*RandomName)
		if ok {
			return storeMap[id]
		}
	}
	lockRandomName.Lock()
	defer lockRandomName.Unlock()
	store, ok = storeRandomName.Load(fitKey)
	if ok {
		tblRandomName, ok := store.(*RandomName)
		if ok {
			return tblRandomName
		}
	}
	tblRandomName := make(map[int64]*RandomName)
	random_name_str, err := consulconfig.GetInstance().GetConfig(strRandomName, option...)
	if random_name_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(random_name_str), &tblRandomName)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "random_name", errUnmarshal)
		return nil
	}
	storeRandomName.Store(fitKey, tblRandomName)
	return tblRandomName[id]
}

func LoadAllRandomNameCfg() error {
	var successChannels []int32
	for _, channel := range consulconfig.GetAllChannelMap() {
		_, err := consulconfig.GetInstance().GetConfig(strRandomName, consulconfig.WithChannel(channel))
		if err != nil {
			continue
		}
		successChannels = append(successChannels, channel)
	}
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "RandomName", successChannels)
	return nil
}
