// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/fancy-common/pkg/repo/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type PurchaseList struct {
	Id             int64  `json:"id"`
	Name           string `json:"name"`
	Mark           string `json:"mark"`
	PaymentChannel int64  `json:"paymentChannel"`
	Icon           string `json:"icon"`
	Sequence       int64  `json:"sequence"`
	DollarCent     int64  `json:"dollarCent"`
	GoodsId        int64  `json:"goodsId"`
	CommodityId    string `json:"commodityId"`
	AddVippt       int64  `json:"addVippt"`
	Price          string `json:"price"`
}

var lockPurchaseList sync.RWMutex
var storePurchaseList sync.Map
var strPurchaseList string = "purchase_list"

func InitPurchaseListCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strPurchaseList, watchPurchaseListFunc)
	return LoadAllPurchaseListCfg()
}

func fixKeyPurchaseList(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strPurchaseList)
}
func watchPurchaseListFunc(key string, js string) {
	mapPurchaseList := make(map[int64]*PurchaseList)
	errUnmarshal := json.Unmarshal([]byte(js), &mapPurchaseList)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storePurchaseList.Store(key, mapPurchaseList)
}

func GetAllPurchaseList(option ...consulconfig.Option) map[int64]*PurchaseList {
	fitKey := fixKeyPurchaseList(option...)
	store, ok := storePurchaseList.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*PurchaseList)
		if ok {
			return storeMap
		}
	}
	lockPurchaseList.Lock()
	defer lockPurchaseList.Unlock()
	store, ok = storePurchaseList.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*PurchaseList)
		if ok {
			return storeMap
		}
	}
	tblPurchaseList := make(map[int64]*PurchaseList)
	purchase_list_str, err := consulconfig.GetInstance().GetConfig(strPurchaseList, option...)
	if purchase_list_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(purchase_list_str), &tblPurchaseList)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "purchase_list", errUnmarshal)
		return nil
	}
	storePurchaseList.Store(fitKey, tblPurchaseList)
	return tblPurchaseList
}

func GetPurchaseList(id int64, option ...consulconfig.Option) *PurchaseList {
	fitKey := fixKeyPurchaseList(option...)
	store, ok := storePurchaseList.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*PurchaseList)
		if ok {
			return storeMap[id]
		}
	}
	lockPurchaseList.Lock()
	defer lockPurchaseList.Unlock()
	store, ok = storePurchaseList.Load(fitKey)
	if ok {
		tblPurchaseList, ok := store.(*PurchaseList)
		if ok {
			return tblPurchaseList
		}
	}
	tblPurchaseList := make(map[int64]*PurchaseList)
	purchase_list_str, err := consulconfig.GetInstance().GetConfig(strPurchaseList, option...)
	if purchase_list_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(purchase_list_str), &tblPurchaseList)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "purchase_list", errUnmarshal)
		return nil
	}
	storePurchaseList.Store(fitKey, tblPurchaseList)
	return tblPurchaseList[id]
}

func LoadAllPurchaseListCfg() error {
	var successChannels []int32
	for _, channel := range consulconfig.GetAllChannelMap() {
		_, err := consulconfig.GetInstance().GetConfig(strPurchaseList, consulconfig.WithChannel(channel))
		if err != nil {
			continue
		}
		successChannels = append(successChannels, channel)
	}
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "PurchaseList", successChannels)
	return nil
}
