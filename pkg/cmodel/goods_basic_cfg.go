// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/fancy-common/pkg/repo/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type GoodsBasic struct {
	Id           int64  `json:"id"`
	Name         string `json:"name"`
	Mark         string `json:"mark"`
	NameLanguage int64  `json:"nameLanguage"`
	ItemId       int64  `json:"itemId"`
	ItemCount    int64  `json:"itemCount"`
	LimitType    int32  `json:"limitType"`
	LimitCount   int32  `json:"limitCount"`
}

var lockGoodsBasic sync.RWMutex
var storeGoodsBasic sync.Map
var strGoodsBasic string = "goods_basic"

func InitGoodsBasicCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strGoodsBasic, watchGoodsBasicFunc)
	return LoadAllGoodsBasicCfg()
}

func fixKeyGoodsBasic(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strGoodsBasic)
}
func watchGoodsBasicFunc(key string, js string) {
	mapGoodsBasic := make(map[int64]*GoodsBasic)
	errUnmarshal := json.Unmarshal([]byte(js), &mapGoodsBasic)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeGoodsBasic.Store(key, mapGoodsBasic)
}

func GetAllGoodsBasic(option ...consulconfig.Option) map[int64]*GoodsBasic {
	fitKey := fixKeyGoodsBasic(option...)
	store, ok := storeGoodsBasic.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*GoodsBasic)
		if ok {
			return storeMap
		}
	}
	lockGoodsBasic.Lock()
	defer lockGoodsBasic.Unlock()
	store, ok = storeGoodsBasic.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*GoodsBasic)
		if ok {
			return storeMap
		}
	}
	tblGoodsBasic := make(map[int64]*GoodsBasic)
	goods_basic_str, err := consulconfig.GetInstance().GetConfig(strGoodsBasic, option...)
	if goods_basic_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(goods_basic_str), &tblGoodsBasic)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "goods_basic", errUnmarshal)
		return nil
	}
	storeGoodsBasic.Store(fitKey, tblGoodsBasic)
	return tblGoodsBasic
}

func GetGoodsBasic(id int64, option ...consulconfig.Option) *GoodsBasic {
	fitKey := fixKeyGoodsBasic(option...)
	store, ok := storeGoodsBasic.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*GoodsBasic)
		if ok {
			return storeMap[id]
		}
	}
	lockGoodsBasic.Lock()
	defer lockGoodsBasic.Unlock()
	store, ok = storeGoodsBasic.Load(fitKey)
	if ok {
		tblGoodsBasic, ok := store.(*GoodsBasic)
		if ok {
			return tblGoodsBasic
		}
	}
	tblGoodsBasic := make(map[int64]*GoodsBasic)
	goods_basic_str, err := consulconfig.GetInstance().GetConfig(strGoodsBasic, option...)
	if goods_basic_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(goods_basic_str), &tblGoodsBasic)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "goods_basic", errUnmarshal)
		return nil
	}
	storeGoodsBasic.Store(fitKey, tblGoodsBasic)
	return tblGoodsBasic[id]
}

func LoadAllGoodsBasicCfg() error {
	var successChannels []int32
	for _, channel := range consulconfig.GetAllChannelMap() {
		_, err := consulconfig.GetInstance().GetConfig(strGoodsBasic, consulconfig.WithChannel(channel))
		if err != nil {
			continue
		}
		successChannels = append(successChannels, channel)
	}
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "GoodsBasic", successChannels)
	return nil
}
