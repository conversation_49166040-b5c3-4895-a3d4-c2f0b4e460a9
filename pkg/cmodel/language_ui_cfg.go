// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/fancy-common/pkg/repo/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type LanguageUi struct {
	Id                int64  `json:"id"`
	Name              string `json:"name"`
	Chinesesimplified string `json:"chinesesimplified"`
	English           string `json:"english"`
	Test              string `json:"test"`
}

var lockLanguageUi sync.RWMutex
var storeLanguageUi sync.Map
var strLanguageUi string = "language_ui"

func InitLanguageUiCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strLanguageUi, watchLanguageUiFunc)
	return LoadAllLanguageUiCfg()
}

func fixKeyLanguageUi(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strLanguageUi)
}
func watchLanguageUiFunc(key string, js string) {
	mapLanguageUi := make(map[int64]*LanguageUi)
	errUnmarshal := json.Unmarshal([]byte(js), &mapLanguageUi)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeLanguageUi.Store(key, mapLanguageUi)
}

func GetAllLanguageUi(option ...consulconfig.Option) map[int64]*LanguageUi {
	fitKey := fixKeyLanguageUi(option...)
	store, ok := storeLanguageUi.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*LanguageUi)
		if ok {
			return storeMap
		}
	}
	lockLanguageUi.Lock()
	defer lockLanguageUi.Unlock()
	store, ok = storeLanguageUi.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*LanguageUi)
		if ok {
			return storeMap
		}
	}
	tblLanguageUi := make(map[int64]*LanguageUi)
	language_ui_str, err := consulconfig.GetInstance().GetConfig(strLanguageUi, option...)
	if language_ui_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(language_ui_str), &tblLanguageUi)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "language_ui", errUnmarshal)
		return nil
	}
	storeLanguageUi.Store(fitKey, tblLanguageUi)
	return tblLanguageUi
}

func GetLanguageUi(id int64, option ...consulconfig.Option) *LanguageUi {
	fitKey := fixKeyLanguageUi(option...)
	store, ok := storeLanguageUi.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*LanguageUi)
		if ok {
			return storeMap[id]
		}
	}
	lockLanguageUi.Lock()
	defer lockLanguageUi.Unlock()
	store, ok = storeLanguageUi.Load(fitKey)
	if ok {
		tblLanguageUi, ok := store.(*LanguageUi)
		if ok {
			return tblLanguageUi
		}
	}
	tblLanguageUi := make(map[int64]*LanguageUi)
	language_ui_str, err := consulconfig.GetInstance().GetConfig(strLanguageUi, option...)
	if language_ui_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(language_ui_str), &tblLanguageUi)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "language_ui", errUnmarshal)
		return nil
	}
	storeLanguageUi.Store(fitKey, tblLanguageUi)
	return tblLanguageUi[id]
}

func LoadAllLanguageUiCfg() error {
	var successChannels []int32
	for _, channel := range consulconfig.GetAllChannelMap() {
		_, err := consulconfig.GetInstance().GetConfig(strLanguageUi, consulconfig.WithChannel(channel))
		if err != nil {
			continue
		}
		successChannels = append(successChannels, channel)
	}
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "LanguageUi", successChannels)
	return nil
}
