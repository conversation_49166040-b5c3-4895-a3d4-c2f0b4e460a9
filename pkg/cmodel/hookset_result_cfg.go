// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/fancy-common/pkg/repo/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type HooksetResult struct {
	Id            int32   `json:"id"`
	Desc          string  `json:"desc"`
	HooksetResult int32   `json:"hooksetResult"`
	ResultFactor  float32 `json:"resultFactor"`
}

var lockHooksetResult sync.RWMutex
var storeHooksetResult sync.Map
var strHooksetResult string = "hookset_result"

func InitHooksetResultCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strHooksetResult, watchHooksetResultFunc)
	return LoadAllHooksetResultCfg()
}

func fixKeyHooksetResult(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strHooksetResult)
}
func watchHooksetResultFunc(key string, js string) {
	mapHooksetResult := make(map[int64]*HooksetResult)
	errUnmarshal := json.Unmarshal([]byte(js), &mapHooksetResult)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeHooksetResult.Store(key, mapHooksetResult)
}

func GetAllHooksetResult(option ...consulconfig.Option) map[int64]*HooksetResult {
	fitKey := fixKeyHooksetResult(option...)
	store, ok := storeHooksetResult.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*HooksetResult)
		if ok {
			return storeMap
		}
	}
	lockHooksetResult.Lock()
	defer lockHooksetResult.Unlock()
	store, ok = storeHooksetResult.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*HooksetResult)
		if ok {
			return storeMap
		}
	}
	tblHooksetResult := make(map[int64]*HooksetResult)
	hookset_result_str, err := consulconfig.GetInstance().GetConfig(strHooksetResult, option...)
	if hookset_result_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(hookset_result_str), &tblHooksetResult)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "hookset_result", errUnmarshal)
		return nil
	}
	storeHooksetResult.Store(fitKey, tblHooksetResult)
	return tblHooksetResult
}

func GetHooksetResult(id int64, option ...consulconfig.Option) *HooksetResult {
	fitKey := fixKeyHooksetResult(option...)
	store, ok := storeHooksetResult.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*HooksetResult)
		if ok {
			return storeMap[id]
		}
	}
	lockHooksetResult.Lock()
	defer lockHooksetResult.Unlock()
	store, ok = storeHooksetResult.Load(fitKey)
	if ok {
		tblHooksetResult, ok := store.(*HooksetResult)
		if ok {
			return tblHooksetResult
		}
	}
	tblHooksetResult := make(map[int64]*HooksetResult)
	hookset_result_str, err := consulconfig.GetInstance().GetConfig(strHooksetResult, option...)
	if hookset_result_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(hookset_result_str), &tblHooksetResult)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "hookset_result", errUnmarshal)
		return nil
	}
	storeHooksetResult.Store(fitKey, tblHooksetResult)
	return tblHooksetResult[id]
}

func LoadAllHooksetResultCfg() error {
	var successChannels []int32
	for _, channel := range consulconfig.GetAllChannelMap() {
		_, err := consulconfig.GetInstance().GetConfig(strHooksetResult, consulconfig.WithChannel(channel))
		if err != nil {
			continue
		}
		successChannels = append(successChannels, channel)
	}
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "HooksetResult", successChannels)
	return nil
}
