// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/fancy-common/pkg/repo/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type LanguageConst struct {
	Id                int64  `json:"id"`
	Name              string `json:"name"`
	Chinesesimplified string `json:"chinesesimplified"`
	English           string `json:"english"`
	Test              string `json:"test"`
}

var lockLanguageConst sync.RWMutex
var storeLanguageConst sync.Map
var strLanguageConst string = "language_const"

func InitLanguageConstCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strLanguageConst, watchLanguageConstFunc)
	return LoadAllLanguageConstCfg()
}

func fixKeyLanguageConst(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strLanguageConst)
}
func watchLanguageConstFunc(key string, js string) {
	mapLanguageConst := make(map[int64]*LanguageConst)
	errUnmarshal := json.Unmarshal([]byte(js), &mapLanguageConst)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeLanguageConst.Store(key, mapLanguageConst)
}

func GetAllLanguageConst(option ...consulconfig.Option) map[int64]*LanguageConst {
	fitKey := fixKeyLanguageConst(option...)
	store, ok := storeLanguageConst.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*LanguageConst)
		if ok {
			return storeMap
		}
	}
	lockLanguageConst.Lock()
	defer lockLanguageConst.Unlock()
	store, ok = storeLanguageConst.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*LanguageConst)
		if ok {
			return storeMap
		}
	}
	tblLanguageConst := make(map[int64]*LanguageConst)
	language_const_str, err := consulconfig.GetInstance().GetConfig(strLanguageConst, option...)
	if language_const_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(language_const_str), &tblLanguageConst)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "language_const", errUnmarshal)
		return nil
	}
	storeLanguageConst.Store(fitKey, tblLanguageConst)
	return tblLanguageConst
}

func GetLanguageConst(id int64, option ...consulconfig.Option) *LanguageConst {
	fitKey := fixKeyLanguageConst(option...)
	store, ok := storeLanguageConst.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*LanguageConst)
		if ok {
			return storeMap[id]
		}
	}
	lockLanguageConst.Lock()
	defer lockLanguageConst.Unlock()
	store, ok = storeLanguageConst.Load(fitKey)
	if ok {
		tblLanguageConst, ok := store.(*LanguageConst)
		if ok {
			return tblLanguageConst
		}
	}
	tblLanguageConst := make(map[int64]*LanguageConst)
	language_const_str, err := consulconfig.GetInstance().GetConfig(strLanguageConst, option...)
	if language_const_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(language_const_str), &tblLanguageConst)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "language_const", errUnmarshal)
		return nil
	}
	storeLanguageConst.Store(fitKey, tblLanguageConst)
	return tblLanguageConst[id]
}

func LoadAllLanguageConstCfg() error {
	var successChannels []int32
	for _, channel := range consulconfig.GetAllChannelMap() {
		_, err := consulconfig.GetInstance().GetConfig(strLanguageConst, consulconfig.WithChannel(channel))
		if err != nil {
			continue
		}
		successChannels = append(successChannels, channel)
	}
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "LanguageConst", successChannels)
	return nil
}
