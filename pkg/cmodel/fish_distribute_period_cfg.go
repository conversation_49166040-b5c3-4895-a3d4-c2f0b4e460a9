// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/fancy-common/pkg/repo/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type FishDistributePeriodPeriod struct {
	PeriodId int64 `json:"periodId"`
	Weight   int32 `json:"weight"`
}

type FishDistributePeriod struct {
	Id     int64                        `json:"id"`
	Name   string                       `json:"name"`
	Period []FishDistributePeriodPeriod `json:"period"`
	Mark   string                       `json:"mark"`
}

var lockFishDistributePeriod sync.RWMutex
var storeFishDistributePeriod sync.Map
var strFishDistributePeriod string = "fish_distribute_period"

func InitFishDistributePeriodCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strFishDistributePeriod, watchFishDistributePeriodFunc)
	return LoadAllFishDistributePeriodCfg()
}

func fixKeyFishDistributePeriod(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strFishDistributePeriod)
}
func watchFishDistributePeriodFunc(key string, js string) {
	mapFishDistributePeriod := make(map[int64]*FishDistributePeriod)
	errUnmarshal := json.Unmarshal([]byte(js), &mapFishDistributePeriod)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeFishDistributePeriod.Store(key, mapFishDistributePeriod)
}

func GetAllFishDistributePeriod(option ...consulconfig.Option) map[int64]*FishDistributePeriod {
	fitKey := fixKeyFishDistributePeriod(option...)
	store, ok := storeFishDistributePeriod.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*FishDistributePeriod)
		if ok {
			return storeMap
		}
	}
	lockFishDistributePeriod.Lock()
	defer lockFishDistributePeriod.Unlock()
	store, ok = storeFishDistributePeriod.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*FishDistributePeriod)
		if ok {
			return storeMap
		}
	}
	tblFishDistributePeriod := make(map[int64]*FishDistributePeriod)
	fish_distribute_period_str, err := consulconfig.GetInstance().GetConfig(strFishDistributePeriod, option...)
	if fish_distribute_period_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(fish_distribute_period_str), &tblFishDistributePeriod)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "fish_distribute_period", errUnmarshal)
		return nil
	}
	storeFishDistributePeriod.Store(fitKey, tblFishDistributePeriod)
	return tblFishDistributePeriod
}

func GetFishDistributePeriod(id int64, option ...consulconfig.Option) *FishDistributePeriod {
	fitKey := fixKeyFishDistributePeriod(option...)
	store, ok := storeFishDistributePeriod.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*FishDistributePeriod)
		if ok {
			return storeMap[id]
		}
	}
	lockFishDistributePeriod.Lock()
	defer lockFishDistributePeriod.Unlock()
	store, ok = storeFishDistributePeriod.Load(fitKey)
	if ok {
		tblFishDistributePeriod, ok := store.(*FishDistributePeriod)
		if ok {
			return tblFishDistributePeriod
		}
	}
	tblFishDistributePeriod := make(map[int64]*FishDistributePeriod)
	fish_distribute_period_str, err := consulconfig.GetInstance().GetConfig(strFishDistributePeriod, option...)
	if fish_distribute_period_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(fish_distribute_period_str), &tblFishDistributePeriod)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "fish_distribute_period", errUnmarshal)
		return nil
	}
	storeFishDistributePeriod.Store(fitKey, tblFishDistributePeriod)
	return tblFishDistributePeriod[id]
}

func LoadAllFishDistributePeriodCfg() error {
	var successChannels []int32
	for _, channel := range consulconfig.GetAllChannelMap() {
		_, err := consulconfig.GetInstance().GetConfig(strFishDistributePeriod, consulconfig.WithChannel(channel))
		if err != nil {
			continue
		}
		successChannels = append(successChannels, channel)
	}
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "FishDistributePeriod", successChannels)
	return nil
}
