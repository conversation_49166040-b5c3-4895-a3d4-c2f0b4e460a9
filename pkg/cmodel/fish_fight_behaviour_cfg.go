// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/fancy-common/pkg/repo/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type FishFightBehaviour struct {
	Id               int64 `json:"id"`
	GenusId          int32 `json:"genusId"`
	SpeciesId        int64 `json:"speciesId"`
	RealbiteId       int64 `json:"realbiteId"`
	FakebiteId       int64 `json:"fakebiteId"`
	KineticProfileId int64 `json:"kineticProfileId"`
}

var lockFishFightBehaviour sync.RWMutex
var storeFishFightBehaviour sync.Map
var strFishFightBehaviour string = "fish_fight_behaviour"

func InitFishFightBehaviourCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strFishFightBehaviour, watchFishFightBehaviourFunc)
	return LoadAllFishFightBehaviourCfg()
}

func fixKeyFishFightBehaviour(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strFishFightBehaviour)
}
func watchFishFightBehaviourFunc(key string, js string) {
	mapFishFightBehaviour := make(map[int64]*FishFightBehaviour)
	errUnmarshal := json.Unmarshal([]byte(js), &mapFishFightBehaviour)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeFishFightBehaviour.Store(key, mapFishFightBehaviour)
}

func GetAllFishFightBehaviour(option ...consulconfig.Option) map[int64]*FishFightBehaviour {
	fitKey := fixKeyFishFightBehaviour(option...)
	store, ok := storeFishFightBehaviour.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*FishFightBehaviour)
		if ok {
			return storeMap
		}
	}
	lockFishFightBehaviour.Lock()
	defer lockFishFightBehaviour.Unlock()
	store, ok = storeFishFightBehaviour.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*FishFightBehaviour)
		if ok {
			return storeMap
		}
	}
	tblFishFightBehaviour := make(map[int64]*FishFightBehaviour)
	fish_fight_behaviour_str, err := consulconfig.GetInstance().GetConfig(strFishFightBehaviour, option...)
	if fish_fight_behaviour_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(fish_fight_behaviour_str), &tblFishFightBehaviour)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "fish_fight_behaviour", errUnmarshal)
		return nil
	}
	storeFishFightBehaviour.Store(fitKey, tblFishFightBehaviour)
	return tblFishFightBehaviour
}

func GetFishFightBehaviour(id int64, option ...consulconfig.Option) *FishFightBehaviour {
	fitKey := fixKeyFishFightBehaviour(option...)
	store, ok := storeFishFightBehaviour.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*FishFightBehaviour)
		if ok {
			return storeMap[id]
		}
	}
	lockFishFightBehaviour.Lock()
	defer lockFishFightBehaviour.Unlock()
	store, ok = storeFishFightBehaviour.Load(fitKey)
	if ok {
		tblFishFightBehaviour, ok := store.(*FishFightBehaviour)
		if ok {
			return tblFishFightBehaviour
		}
	}
	tblFishFightBehaviour := make(map[int64]*FishFightBehaviour)
	fish_fight_behaviour_str, err := consulconfig.GetInstance().GetConfig(strFishFightBehaviour, option...)
	if fish_fight_behaviour_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(fish_fight_behaviour_str), &tblFishFightBehaviour)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "fish_fight_behaviour", errUnmarshal)
		return nil
	}
	storeFishFightBehaviour.Store(fitKey, tblFishFightBehaviour)
	return tblFishFightBehaviour[id]
}

func LoadAllFishFightBehaviourCfg() error {
	var successChannels []int32
	for _, channel := range consulconfig.GetAllChannelMap() {
		_, err := consulconfig.GetInstance().GetConfig(strFishFightBehaviour, consulconfig.WithChannel(channel))
		if err != nil {
			continue
		}
		successChannels = append(successChannels, channel)
	}
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "FishFightBehaviour", successChannels)
	return nil
}
