// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/fancy-common/pkg/repo/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type LanguageFishLangName struct {
	Chinesesimplified string `json:"chinesesimplified"`
	English           string `json:"english"`
	Test              string `json:"test"`
}

type LanguageFishLangDes struct {
	Chinesesimplified string `json:"chinesesimplified"`
	English           string `json:"english"`
	Test              string `json:"test"`
}

type LanguageFish struct {
	Id       int64                `json:"id"`
	Name     string               `json:"name"`
	LangName LanguageFishLangName `json:"lang_name"`
	LangDes  LanguageFishLangDes  `json:"lang_des"`
}

var lockLanguageFish sync.RWMutex
var storeLanguageFish sync.Map
var strLanguageFish string = "language_fish"

func InitLanguageFishCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strLanguageFish, watchLanguageFishFunc)
	return LoadAllLanguageFishCfg()
}

func fixKeyLanguageFish(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strLanguageFish)
}
func watchLanguageFishFunc(key string, js string) {
	mapLanguageFish := make(map[int64]*LanguageFish)
	errUnmarshal := json.Unmarshal([]byte(js), &mapLanguageFish)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeLanguageFish.Store(key, mapLanguageFish)
}

func GetAllLanguageFish(option ...consulconfig.Option) map[int64]*LanguageFish {
	fitKey := fixKeyLanguageFish(option...)
	store, ok := storeLanguageFish.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*LanguageFish)
		if ok {
			return storeMap
		}
	}
	lockLanguageFish.Lock()
	defer lockLanguageFish.Unlock()
	store, ok = storeLanguageFish.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*LanguageFish)
		if ok {
			return storeMap
		}
	}
	tblLanguageFish := make(map[int64]*LanguageFish)
	language_fish_str, err := consulconfig.GetInstance().GetConfig(strLanguageFish, option...)
	if language_fish_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(language_fish_str), &tblLanguageFish)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "language_fish", errUnmarshal)
		return nil
	}
	storeLanguageFish.Store(fitKey, tblLanguageFish)
	return tblLanguageFish
}

func GetLanguageFish(id int64, option ...consulconfig.Option) *LanguageFish {
	fitKey := fixKeyLanguageFish(option...)
	store, ok := storeLanguageFish.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*LanguageFish)
		if ok {
			return storeMap[id]
		}
	}
	lockLanguageFish.Lock()
	defer lockLanguageFish.Unlock()
	store, ok = storeLanguageFish.Load(fitKey)
	if ok {
		tblLanguageFish, ok := store.(*LanguageFish)
		if ok {
			return tblLanguageFish
		}
	}
	tblLanguageFish := make(map[int64]*LanguageFish)
	language_fish_str, err := consulconfig.GetInstance().GetConfig(strLanguageFish, option...)
	if language_fish_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(language_fish_str), &tblLanguageFish)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "language_fish", errUnmarshal)
		return nil
	}
	storeLanguageFish.Store(fitKey, tblLanguageFish)
	return tblLanguageFish[id]
}

func LoadAllLanguageFishCfg() error {
	var successChannels []int32
	for _, channel := range consulconfig.GetAllChannelMap() {
		_, err := consulconfig.GetInstance().GetConfig(strLanguageFish, consulconfig.WithChannel(channel))
		if err != nil {
			continue
		}
		successChannels = append(successChannels, channel)
	}
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "LanguageFish", successChannels)
	return nil
}
