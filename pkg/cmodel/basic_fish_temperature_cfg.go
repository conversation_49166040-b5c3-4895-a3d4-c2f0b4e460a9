// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/fancy-common/pkg/repo/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type BasicFishTemperatureTemperature struct {
	Min int32 `json:"min"`
	Max int32 `json:"max"`
}

type BasicFishTemperature struct {
	Id          int64                           `json:"id"`
	Name        string                          `json:"name"`
	Description string                          `json:"description"`
	Temperature BasicFishTemperatureTemperature `json:"temperature"`
}

var lockBasicFishTemperature sync.RWMutex
var storeBasicFishTemperature sync.Map
var strBasicFishTemperature string = "basic_fish_temperature"

func InitBasicFishTemperatureCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strBasicFishTemperature, watchBasicFishTemperatureFunc)
	return LoadAllBasicFishTemperatureCfg()
}

func fixKeyBasicFishTemperature(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strBasicFishTemperature)
}
func watchBasicFishTemperatureFunc(key string, js string) {
	mapBasicFishTemperature := make(map[int64]*BasicFishTemperature)
	errUnmarshal := json.Unmarshal([]byte(js), &mapBasicFishTemperature)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeBasicFishTemperature.Store(key, mapBasicFishTemperature)
}

func GetAllBasicFishTemperature(option ...consulconfig.Option) map[int64]*BasicFishTemperature {
	fitKey := fixKeyBasicFishTemperature(option...)
	store, ok := storeBasicFishTemperature.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*BasicFishTemperature)
		if ok {
			return storeMap
		}
	}
	lockBasicFishTemperature.Lock()
	defer lockBasicFishTemperature.Unlock()
	store, ok = storeBasicFishTemperature.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*BasicFishTemperature)
		if ok {
			return storeMap
		}
	}
	tblBasicFishTemperature := make(map[int64]*BasicFishTemperature)
	basic_fish_temperature_str, err := consulconfig.GetInstance().GetConfig(strBasicFishTemperature, option...)
	if basic_fish_temperature_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(basic_fish_temperature_str), &tblBasicFishTemperature)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "basic_fish_temperature", errUnmarshal)
		return nil
	}
	storeBasicFishTemperature.Store(fitKey, tblBasicFishTemperature)
	return tblBasicFishTemperature
}

func GetBasicFishTemperature(id int64, option ...consulconfig.Option) *BasicFishTemperature {
	fitKey := fixKeyBasicFishTemperature(option...)
	store, ok := storeBasicFishTemperature.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*BasicFishTemperature)
		if ok {
			return storeMap[id]
		}
	}
	lockBasicFishTemperature.Lock()
	defer lockBasicFishTemperature.Unlock()
	store, ok = storeBasicFishTemperature.Load(fitKey)
	if ok {
		tblBasicFishTemperature, ok := store.(*BasicFishTemperature)
		if ok {
			return tblBasicFishTemperature
		}
	}
	tblBasicFishTemperature := make(map[int64]*BasicFishTemperature)
	basic_fish_temperature_str, err := consulconfig.GetInstance().GetConfig(strBasicFishTemperature, option...)
	if basic_fish_temperature_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(basic_fish_temperature_str), &tblBasicFishTemperature)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "basic_fish_temperature", errUnmarshal)
		return nil
	}
	storeBasicFishTemperature.Store(fitKey, tblBasicFishTemperature)
	return tblBasicFishTemperature[id]
}

func LoadAllBasicFishTemperatureCfg() error {
	var successChannels []int32
	for _, channel := range consulconfig.GetAllChannelMap() {
		_, err := consulconfig.GetInstance().GetConfig(strBasicFishTemperature, consulconfig.WithChannel(channel))
		if err != nil {
			continue
		}
		successChannels = append(successChannels, channel)
	}
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "BasicFishTemperature", successChannels)
	return nil
}
