// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/fancy-common/pkg/repo/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type Task struct {
	Id           int64  `json:"id"`
	Name         string `json:"name"`
	NameLanguage int64  `json:"nameLanguage"`
	DescLanguage int64  `json:"descLanguage"`
	Type         int32  `json:"type"`
	SubId        int64  `json:"subId"`
	Group        int64  `json:"group"`
	Sort         int64  `json:"sort"`
	CondGroup    int64  `json:"condGroup"`
	Reward       int64  `json:"reward"`
	Score        int64  `json:"score"`
}

var lockTask sync.RWMutex
var storeTask sync.Map
var strTask string = "task"

func InitTaskCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strTask, watchTaskFunc)
	return LoadAllTaskCfg()
}

func fixKeyTask(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strTask)
}
func watchTaskFunc(key string, js string) {
	mapTask := make(map[int64]*Task)
	errUnmarshal := json.Unmarshal([]byte(js), &mapTask)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeTask.Store(key, mapTask)
}

func GetAllTask(option ...consulconfig.Option) map[int64]*Task {
	fitKey := fixKeyTask(option...)
	store, ok := storeTask.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*Task)
		if ok {
			return storeMap
		}
	}
	lockTask.Lock()
	defer lockTask.Unlock()
	store, ok = storeTask.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*Task)
		if ok {
			return storeMap
		}
	}
	tblTask := make(map[int64]*Task)
	task_str, err := consulconfig.GetInstance().GetConfig(strTask, option...)
	if task_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(task_str), &tblTask)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "task", errUnmarshal)
		return nil
	}
	storeTask.Store(fitKey, tblTask)
	return tblTask
}

func GetTask(id int64, option ...consulconfig.Option) *Task {
	fitKey := fixKeyTask(option...)
	store, ok := storeTask.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*Task)
		if ok {
			return storeMap[id]
		}
	}
	lockTask.Lock()
	defer lockTask.Unlock()
	store, ok = storeTask.Load(fitKey)
	if ok {
		tblTask, ok := store.(*Task)
		if ok {
			return tblTask
		}
	}
	tblTask := make(map[int64]*Task)
	task_str, err := consulconfig.GetInstance().GetConfig(strTask, option...)
	if task_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(task_str), &tblTask)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "task", errUnmarshal)
		return nil
	}
	storeTask.Store(fitKey, tblTask)
	return tblTask[id]
}

func LoadAllTaskCfg() error {
	var successChannels []int32
	for _, channel := range consulconfig.GetAllChannelMap() {
		_, err := consulconfig.GetInstance().GetConfig(strTask, consulconfig.WithChannel(channel))
		if err != nil {
			continue
		}
		successChannels = append(successChannels, channel)
	}
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "Task", successChannels)
	return nil
}
