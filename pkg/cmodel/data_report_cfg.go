// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/fancy-common/pkg/repo/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type DataReport struct {
	Id         int64 `json:"id"`
	ModuleType int32 `json:"moduleType"`
	Open       int32 `json:"open"`
}

var lockDataReport sync.RWMutex
var storeDataReport sync.Map
var strDataReport string = "data_report"

func InitDataReportCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strDataReport, watchDataReportFunc)
	return LoadAllDataReportCfg()
}

func fixKeyDataReport(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strDataReport)
}
func watchDataReportFunc(key string, js string) {
	mapDataReport := make(map[int64]*DataReport)
	errUnmarshal := json.Unmarshal([]byte(js), &mapDataReport)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeDataReport.Store(key, mapDataReport)
}

func GetAllDataReport(option ...consulconfig.Option) map[int64]*DataReport {
	fitKey := fixKeyDataReport(option...)
	store, ok := storeDataReport.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*DataReport)
		if ok {
			return storeMap
		}
	}
	lockDataReport.Lock()
	defer lockDataReport.Unlock()
	store, ok = storeDataReport.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*DataReport)
		if ok {
			return storeMap
		}
	}
	tblDataReport := make(map[int64]*DataReport)
	data_report_str, err := consulconfig.GetInstance().GetConfig(strDataReport, option...)
	if data_report_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(data_report_str), &tblDataReport)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "data_report", errUnmarshal)
		return nil
	}
	storeDataReport.Store(fitKey, tblDataReport)
	return tblDataReport
}

func GetDataReport(id int64, option ...consulconfig.Option) *DataReport {
	fitKey := fixKeyDataReport(option...)
	store, ok := storeDataReport.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*DataReport)
		if ok {
			return storeMap[id]
		}
	}
	lockDataReport.Lock()
	defer lockDataReport.Unlock()
	store, ok = storeDataReport.Load(fitKey)
	if ok {
		tblDataReport, ok := store.(*DataReport)
		if ok {
			return tblDataReport
		}
	}
	tblDataReport := make(map[int64]*DataReport)
	data_report_str, err := consulconfig.GetInstance().GetConfig(strDataReport, option...)
	if data_report_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(data_report_str), &tblDataReport)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "data_report", errUnmarshal)
		return nil
	}
	storeDataReport.Store(fitKey, tblDataReport)
	return tblDataReport[id]
}

func LoadAllDataReportCfg() error {
	var successChannels []int32
	for _, channel := range consulconfig.GetAllChannelMap() {
		_, err := consulconfig.GetInstance().GetConfig(strDataReport, consulconfig.WithChannel(channel))
		if err != nil {
			continue
		}
		successChannels = append(successChannels, channel)
	}
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "DataReport", successChannels)
	return nil
}
