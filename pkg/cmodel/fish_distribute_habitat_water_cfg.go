// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/fancy-common/pkg/repo/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type FishDistributeHabitatWaterHabitatWater struct {
	HabitatWaterId int64 `json:"habitatWaterId"`
	Weight         int32 `json:"weight"`
}

type FishDistributeHabitatWater struct {
	Id           int64                                    `json:"id"`
	Name         string                                   `json:"name"`
	HabitatWater []FishDistributeHabitatWaterHabitatWater `json:"habitatWater"`
}

var lockFishDistributeHabitatWater sync.RWMutex
var storeFishDistributeHabitatWater sync.Map
var strFishDistributeHabitatWater string = "fish_distribute_habitat_water"

func InitFishDistributeHabitatWaterCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strFishDistributeHabitatWater, watchFishDistributeHabitatWaterFunc)
	return LoadAllFishDistributeHabitatWaterCfg()
}

func fixKeyFishDistributeHabitatWater(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strFishDistributeHabitatWater)
}
func watchFishDistributeHabitatWaterFunc(key string, js string) {
	mapFishDistributeHabitatWater := make(map[int64]*FishDistributeHabitatWater)
	errUnmarshal := json.Unmarshal([]byte(js), &mapFishDistributeHabitatWater)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeFishDistributeHabitatWater.Store(key, mapFishDistributeHabitatWater)
}

func GetAllFishDistributeHabitatWater(option ...consulconfig.Option) map[int64]*FishDistributeHabitatWater {
	fitKey := fixKeyFishDistributeHabitatWater(option...)
	store, ok := storeFishDistributeHabitatWater.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*FishDistributeHabitatWater)
		if ok {
			return storeMap
		}
	}
	lockFishDistributeHabitatWater.Lock()
	defer lockFishDistributeHabitatWater.Unlock()
	store, ok = storeFishDistributeHabitatWater.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*FishDistributeHabitatWater)
		if ok {
			return storeMap
		}
	}
	tblFishDistributeHabitatWater := make(map[int64]*FishDistributeHabitatWater)
	fish_distribute_habitat_water_str, err := consulconfig.GetInstance().GetConfig(strFishDistributeHabitatWater, option...)
	if fish_distribute_habitat_water_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(fish_distribute_habitat_water_str), &tblFishDistributeHabitatWater)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "fish_distribute_habitat_water", errUnmarshal)
		return nil
	}
	storeFishDistributeHabitatWater.Store(fitKey, tblFishDistributeHabitatWater)
	return tblFishDistributeHabitatWater
}

func GetFishDistributeHabitatWater(id int64, option ...consulconfig.Option) *FishDistributeHabitatWater {
	fitKey := fixKeyFishDistributeHabitatWater(option...)
	store, ok := storeFishDistributeHabitatWater.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*FishDistributeHabitatWater)
		if ok {
			return storeMap[id]
		}
	}
	lockFishDistributeHabitatWater.Lock()
	defer lockFishDistributeHabitatWater.Unlock()
	store, ok = storeFishDistributeHabitatWater.Load(fitKey)
	if ok {
		tblFishDistributeHabitatWater, ok := store.(*FishDistributeHabitatWater)
		if ok {
			return tblFishDistributeHabitatWater
		}
	}
	tblFishDistributeHabitatWater := make(map[int64]*FishDistributeHabitatWater)
	fish_distribute_habitat_water_str, err := consulconfig.GetInstance().GetConfig(strFishDistributeHabitatWater, option...)
	if fish_distribute_habitat_water_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(fish_distribute_habitat_water_str), &tblFishDistributeHabitatWater)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "fish_distribute_habitat_water", errUnmarshal)
		return nil
	}
	storeFishDistributeHabitatWater.Store(fitKey, tblFishDistributeHabitatWater)
	return tblFishDistributeHabitatWater[id]
}

func LoadAllFishDistributeHabitatWaterCfg() error {
	var successChannels []int32
	for _, channel := range consulconfig.GetAllChannelMap() {
		_, err := consulconfig.GetInstance().GetConfig(strFishDistributeHabitatWater, consulconfig.WithChannel(channel))
		if err != nil {
			continue
		}
		successChannels = append(successChannels, channel)
	}
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "FishDistributeHabitatWater", successChannels)
	return nil
}
