package crpc_stats

import (
	"context"
	"fmt"
	"sync"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	statsRpc "git.keepfancy.xyz/back-end/fancy-common/pkg/intranetrpc/statsrpc"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/dictionary/dict_common"
	grpcHa "git.keepfancy.xyz/back-end/frameworks/kit/rpc"
	"github.com/sirupsen/logrus"
)

type StatsRpcClient struct {
}

var (
	once   = &sync.Once{}
	single *StatsRpcClient
)

func GetStatsRpcInstance() *StatsRpcClient {
	if single != nil {
		return single
	}

	once.Do(func() {
		single = &StatsRpcClient{}
	})
	return single
}

// GetStatsRpcClient 获取stats rpc client
func (c *StatsRpcClient) GetStatsRpcClient() statsRpc.StatsServiceClient {
	cc, err := grpcHa.GetConnection(dict_common.StatsSrv)
	if err != nil {
		logrus.Errorf("rpc get conn err : %v", err)
		return nil
	}

	return statsRpc.NewStatsServiceClient(cc)
}

// RpcGetStatsList 获取统计列表
func RpcGetStatsList(ctx context.Context, productId int32, playerId uint64) ([]*commonPB.StatInfo, error) {
	if playerId <= 0 {
		return nil, fmt.Errorf("playerId is invalid")
	}

	rpcClient := GetStatsRpcInstance().GetStatsRpcClient()
	req := &statsRpc.GetStatsListReq{
		ProductId: productId,
		PlayerId:  playerId,
	}

	rsp, err := rpcClient.GetStatsList(ctx, req)
	if err != nil {
		logrus.Errorf("get stats list failed:%+v req:%+v", err, req)
		return nil, err
	}

	return rsp.List, nil
}
