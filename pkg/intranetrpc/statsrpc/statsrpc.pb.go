// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v5.27.0
// source: statsrpc/statsrpc.proto

package statsRpc

import (
	common "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GetStatsListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ProductId int32  `protobuf:"varint,1,opt,name=product_id,json=productId,proto3" json:"product_id,omitempty"` // 产品 ID
	PlayerId  uint64 `protobuf:"varint,2,opt,name=player_id,json=playerId,proto3" json:"player_id,omitempty"`    // 玩家id
}

func (x *GetStatsListReq) Reset() {
	*x = GetStatsListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_statsrpc_statsrpc_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetStatsListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetStatsListReq) ProtoMessage() {}

func (x *GetStatsListReq) ProtoReflect() protoreflect.Message {
	mi := &file_statsrpc_statsrpc_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetStatsListReq.ProtoReflect.Descriptor instead.
func (*GetStatsListReq) Descriptor() ([]byte, []int) {
	return file_statsrpc_statsrpc_proto_rawDescGZIP(), []int{0}
}

func (x *GetStatsListReq) GetProductId() int32 {
	if x != nil {
		return x.ProductId
	}
	return 0
}

func (x *GetStatsListReq) GetPlayerId() uint64 {
	if x != nil {
		return x.PlayerId
	}
	return 0
}

type GetStatsListRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret       *common.Result     `protobuf:"bytes,1,opt,name=ret,proto3" json:"ret,omitempty"`
	ProductId int32              `protobuf:"varint,2,opt,name=product_id,json=productId,proto3" json:"product_id,omitempty"` // 产品 ID
	PlayerId  uint64             `protobuf:"varint,3,opt,name=player_id,json=playerId,proto3" json:"player_id,omitempty"`    // 玩家id
	List      []*common.StatInfo `protobuf:"bytes,4,rep,name=list,proto3" json:"list,omitempty"`                             // 统计信息
}

func (x *GetStatsListRsp) Reset() {
	*x = GetStatsListRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_statsrpc_statsrpc_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetStatsListRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetStatsListRsp) ProtoMessage() {}

func (x *GetStatsListRsp) ProtoReflect() protoreflect.Message {
	mi := &file_statsrpc_statsrpc_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetStatsListRsp.ProtoReflect.Descriptor instead.
func (*GetStatsListRsp) Descriptor() ([]byte, []int) {
	return file_statsrpc_statsrpc_proto_rawDescGZIP(), []int{1}
}

func (x *GetStatsListRsp) GetRet() *common.Result {
	if x != nil {
		return x.Ret
	}
	return nil
}

func (x *GetStatsListRsp) GetProductId() int32 {
	if x != nil {
		return x.ProductId
	}
	return 0
}

func (x *GetStatsListRsp) GetPlayerId() uint64 {
	if x != nil {
		return x.PlayerId
	}
	return 0
}

func (x *GetStatsListRsp) GetList() []*common.StatInfo {
	if x != nil {
		return x.List
	}
	return nil
}

var File_statsrpc_statsrpc_proto protoreflect.FileDescriptor

var file_statsrpc_statsrpc_proto_rawDesc = []byte{
	0x0a, 0x17, 0x73, 0x74, 0x61, 0x74, 0x73, 0x72, 0x70, 0x63, 0x2f, 0x73, 0x74, 0x61, 0x74, 0x73,
	0x72, 0x70, 0x63, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x08, 0x73, 0x74, 0x61, 0x74, 0x73,
	0x52, 0x70, 0x63, 0x1a, 0x0c, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x0c, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22,
	0x4d, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x53, 0x74, 0x61, 0x74, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x52,
	0x65, 0x71, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x49,
	0x64, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x64, 0x22, 0x95,
	0x01, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x53, 0x74, 0x61, 0x74, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x52,
	0x73, 0x70, 0x12, 0x20, 0x0a, 0x03, 0x72, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x0e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52,
	0x03, 0x72, 0x65, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63,
	0x74, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x5f, 0x69, 0x64,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x64,
	0x12, 0x24, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x10,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x32, 0x54, 0x0a, 0x0c, 0x73, 0x74, 0x61, 0x74, 0x73, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x44, 0x0a, 0x0c, 0x47, 0x65, 0x74, 0x53, 0x74, 0x61,
	0x74, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x19, 0x2e, 0x73, 0x74, 0x61, 0x74, 0x73, 0x52, 0x70,
	0x63, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x74, 0x61, 0x74, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65,
	0x71, 0x1a, 0x19, 0x2e, 0x73, 0x74, 0x61, 0x74, 0x73, 0x52, 0x70, 0x63, 0x2e, 0x47, 0x65, 0x74,
	0x53, 0x74, 0x61, 0x74, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x73, 0x70, 0x42, 0x0c, 0x5a, 0x0a,
	0x2e, 0x3b, 0x73, 0x74, 0x61, 0x74, 0x73, 0x52, 0x70, 0x63, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var (
	file_statsrpc_statsrpc_proto_rawDescOnce sync.Once
	file_statsrpc_statsrpc_proto_rawDescData = file_statsrpc_statsrpc_proto_rawDesc
)

func file_statsrpc_statsrpc_proto_rawDescGZIP() []byte {
	file_statsrpc_statsrpc_proto_rawDescOnce.Do(func() {
		file_statsrpc_statsrpc_proto_rawDescData = protoimpl.X.CompressGZIP(file_statsrpc_statsrpc_proto_rawDescData)
	})
	return file_statsrpc_statsrpc_proto_rawDescData
}

var file_statsrpc_statsrpc_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_statsrpc_statsrpc_proto_goTypes = []interface{}{
	(*GetStatsListReq)(nil), // 0: statsRpc.GetStatsListReq
	(*GetStatsListRsp)(nil), // 1: statsRpc.GetStatsListRsp
	(*common.Result)(nil),   // 2: common.Result
	(*common.StatInfo)(nil), // 3: common.StatInfo
}
var file_statsrpc_statsrpc_proto_depIdxs = []int32{
	2, // 0: statsRpc.GetStatsListRsp.ret:type_name -> common.Result
	3, // 1: statsRpc.GetStatsListRsp.list:type_name -> common.StatInfo
	0, // 2: statsRpc.statsService.GetStatsList:input_type -> statsRpc.GetStatsListReq
	1, // 3: statsRpc.statsService.GetStatsList:output_type -> statsRpc.GetStatsListRsp
	3, // [3:4] is the sub-list for method output_type
	2, // [2:3] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_statsrpc_statsrpc_proto_init() }
func file_statsrpc_statsrpc_proto_init() {
	if File_statsrpc_statsrpc_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_statsrpc_statsrpc_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetStatsListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_statsrpc_statsrpc_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetStatsListRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_statsrpc_statsrpc_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_statsrpc_statsrpc_proto_goTypes,
		DependencyIndexes: file_statsrpc_statsrpc_proto_depIdxs,
		MessageInfos:      file_statsrpc_statsrpc_proto_msgTypes,
	}.Build()
	File_statsrpc_statsrpc_proto = out.File
	file_statsrpc_statsrpc_proto_rawDesc = nil
	file_statsrpc_statsrpc_proto_goTypes = nil
	file_statsrpc_statsrpc_proto_depIdxs = nil
}
