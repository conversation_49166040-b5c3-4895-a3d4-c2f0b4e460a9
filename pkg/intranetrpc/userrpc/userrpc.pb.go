// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v5.27.0
// source: userrpc/userrpc.proto

package userRpc

import (
	common "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 创建玩家请求
type CreatePlayerReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ClientVersion string              `protobuf:"bytes,1,opt,name=client_version,json=clientVersion,proto3" json:"client_version,omitempty"`               // 客户端版本号
	ProductId     int32               `protobuf:"varint,2,opt,name=product_id,json=productId,proto3" json:"product_id,omitempty"`                          // 产品 ID
	ChannelId     common.CHANNEL_TYPE `protobuf:"varint,3,opt,name=channel_id,json=channelId,proto3,enum=common.CHANNEL_TYPE" json:"channel_id,omitempty"` // 渠道 ID
	DeviceInfo    *common.DeviceInfo  `protobuf:"bytes,4,opt,name=device_info,json=deviceInfo,proto3" json:"device_info,omitempty"`                        // 设备信息
	ThirdToken    string              `protobuf:"bytes,5,opt,name=third_token,json=thirdToken,proto3" json:"third_token,omitempty"`                        // 三方Token，根据具体SDK
	// string                adjust_id         = 6;  // adjust id (废弃， device_info 中包含)
	Network     common.NETWORK_TYPE    `protobuf:"varint,7,opt,name=network,proto3,enum=common.NETWORK_TYPE" json:"network,omitempty"`                        // 网络类型
	BundleName  string                 `protobuf:"bytes,8,opt,name=bundle_name,json=bundleName,proto3" json:"bundle_name,omitempty"`                          // 包名
	Platform    common.PLATFORM_TYPE   `protobuf:"varint,9,opt,name=platform,proto3,enum=common.PLATFORM_TYPE" json:"platform,omitempty"`                     // 平台
	AccountInfo *common.AccountInfo    `protobuf:"bytes,10,opt,name=account_info,json=accountInfo,proto3" json:"account_info,omitempty"`                      // 账号信息
	CreateType  common.LOGIN_TYPE      `protobuf:"varint,11,opt,name=create_type,json=createType,proto3,enum=common.LOGIN_TYPE" json:"create_type,omitempty"` // 创建方式
	ThirdInfo   *common.ThirdLoginInfo `protobuf:"bytes,12,opt,name=third_info,json=thirdInfo,proto3" json:"third_info,omitempty"`                            // 三方信息
}

func (x *CreatePlayerReq) Reset() {
	*x = CreatePlayerReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_userrpc_userrpc_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreatePlayerReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreatePlayerReq) ProtoMessage() {}

func (x *CreatePlayerReq) ProtoReflect() protoreflect.Message {
	mi := &file_userrpc_userrpc_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreatePlayerReq.ProtoReflect.Descriptor instead.
func (*CreatePlayerReq) Descriptor() ([]byte, []int) {
	return file_userrpc_userrpc_proto_rawDescGZIP(), []int{0}
}

func (x *CreatePlayerReq) GetClientVersion() string {
	if x != nil {
		return x.ClientVersion
	}
	return ""
}

func (x *CreatePlayerReq) GetProductId() int32 {
	if x != nil {
		return x.ProductId
	}
	return 0
}

func (x *CreatePlayerReq) GetChannelId() common.CHANNEL_TYPE {
	if x != nil {
		return x.ChannelId
	}
	return common.CHANNEL_TYPE(0)
}

func (x *CreatePlayerReq) GetDeviceInfo() *common.DeviceInfo {
	if x != nil {
		return x.DeviceInfo
	}
	return nil
}

func (x *CreatePlayerReq) GetThirdToken() string {
	if x != nil {
		return x.ThirdToken
	}
	return ""
}

func (x *CreatePlayerReq) GetNetwork() common.NETWORK_TYPE {
	if x != nil {
		return x.Network
	}
	return common.NETWORK_TYPE(0)
}

func (x *CreatePlayerReq) GetBundleName() string {
	if x != nil {
		return x.BundleName
	}
	return ""
}

func (x *CreatePlayerReq) GetPlatform() common.PLATFORM_TYPE {
	if x != nil {
		return x.Platform
	}
	return common.PLATFORM_TYPE(0)
}

func (x *CreatePlayerReq) GetAccountInfo() *common.AccountInfo {
	if x != nil {
		return x.AccountInfo
	}
	return nil
}

func (x *CreatePlayerReq) GetCreateType() common.LOGIN_TYPE {
	if x != nil {
		return x.CreateType
	}
	return common.LOGIN_TYPE(0)
}

func (x *CreatePlayerReq) GetThirdInfo() *common.ThirdLoginInfo {
	if x != nil {
		return x.ThirdInfo
	}
	return nil
}

// 创建玩家返回
type CreatePlayerRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PlayerId     uint64               `protobuf:"varint,1,opt,name=player_id,json=playerId,proto3" json:"player_id,omitempty"`              //玩家id
	RichUserInfo *common.RichUserInfo `protobuf:"bytes,2,opt,name=rich_user_info,json=richUserInfo,proto3" json:"rich_user_info,omitempty"` //全量用户信息
}

func (x *CreatePlayerRsp) Reset() {
	*x = CreatePlayerRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_userrpc_userrpc_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreatePlayerRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreatePlayerRsp) ProtoMessage() {}

func (x *CreatePlayerRsp) ProtoReflect() protoreflect.Message {
	mi := &file_userrpc_userrpc_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreatePlayerRsp.ProtoReflect.Descriptor instead.
func (*CreatePlayerRsp) Descriptor() ([]byte, []int) {
	return file_userrpc_userrpc_proto_rawDescGZIP(), []int{1}
}

func (x *CreatePlayerRsp) GetPlayerId() uint64 {
	if x != nil {
		return x.PlayerId
	}
	return 0
}

func (x *CreatePlayerRsp) GetRichUserInfo() *common.RichUserInfo {
	if x != nil {
		return x.RichUserInfo
	}
	return nil
}

// 获取玩家信息请求
type GetPlayerInfoReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ProductId int32  `protobuf:"varint,1,opt,name=product_id,json=productId,proto3" json:"product_id,omitempty"` //产品id
	PlayerId  uint64 `protobuf:"varint,2,opt,name=player_id,json=playerId,proto3" json:"player_id,omitempty"`    //玩家id
}

func (x *GetPlayerInfoReq) Reset() {
	*x = GetPlayerInfoReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_userrpc_userrpc_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPlayerInfoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPlayerInfoReq) ProtoMessage() {}

func (x *GetPlayerInfoReq) ProtoReflect() protoreflect.Message {
	mi := &file_userrpc_userrpc_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPlayerInfoReq.ProtoReflect.Descriptor instead.
func (*GetPlayerInfoReq) Descriptor() ([]byte, []int) {
	return file_userrpc_userrpc_proto_rawDescGZIP(), []int{2}
}

func (x *GetPlayerInfoReq) GetProductId() int32 {
	if x != nil {
		return x.ProductId
	}
	return 0
}

func (x *GetPlayerInfoReq) GetPlayerId() uint64 {
	if x != nil {
		return x.PlayerId
	}
	return 0
}

// 获取玩家信息返回
type GetPlayerInfoRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PlayerId     uint64               `protobuf:"varint,1,opt,name=player_id,json=playerId,proto3" json:"player_id,omitempty"`              //玩家id
	RichUserInfo *common.RichUserInfo `protobuf:"bytes,2,opt,name=rich_user_info,json=richUserInfo,proto3" json:"rich_user_info,omitempty"` //全量用户信息
}

func (x *GetPlayerInfoRsp) Reset() {
	*x = GetPlayerInfoRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_userrpc_userrpc_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPlayerInfoRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPlayerInfoRsp) ProtoMessage() {}

func (x *GetPlayerInfoRsp) ProtoReflect() protoreflect.Message {
	mi := &file_userrpc_userrpc_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPlayerInfoRsp.ProtoReflect.Descriptor instead.
func (*GetPlayerInfoRsp) Descriptor() ([]byte, []int) {
	return file_userrpc_userrpc_proto_rawDescGZIP(), []int{3}
}

func (x *GetPlayerInfoRsp) GetPlayerId() uint64 {
	if x != nil {
		return x.PlayerId
	}
	return 0
}

func (x *GetPlayerInfoRsp) GetRichUserInfo() *common.RichUserInfo {
	if x != nil {
		return x.RichUserInfo
	}
	return nil
}

// 根据deviceId获取玩家id请求
type GetPlayerIdByDeviceReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ProductId  int32  `protobuf:"varint,1,opt,name=product_id,json=productId,proto3" json:"product_id,omitempty"`   //产品id
	DeviceCode string `protobuf:"bytes,2,opt,name=device_code,json=deviceCode,proto3" json:"device_code,omitempty"` //设备码
}

func (x *GetPlayerIdByDeviceReq) Reset() {
	*x = GetPlayerIdByDeviceReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_userrpc_userrpc_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPlayerIdByDeviceReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPlayerIdByDeviceReq) ProtoMessage() {}

func (x *GetPlayerIdByDeviceReq) ProtoReflect() protoreflect.Message {
	mi := &file_userrpc_userrpc_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPlayerIdByDeviceReq.ProtoReflect.Descriptor instead.
func (*GetPlayerIdByDeviceReq) Descriptor() ([]byte, []int) {
	return file_userrpc_userrpc_proto_rawDescGZIP(), []int{4}
}

func (x *GetPlayerIdByDeviceReq) GetProductId() int32 {
	if x != nil {
		return x.ProductId
	}
	return 0
}

func (x *GetPlayerIdByDeviceReq) GetDeviceCode() string {
	if x != nil {
		return x.DeviceCode
	}
	return ""
}

// 根据deviceId获取玩家id返回
type GetPlayerIdByDeviceRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PlayerId uint64 `protobuf:"varint,1,opt,name=player_id,json=playerId,proto3" json:"player_id,omitempty"` //玩家id
}

func (x *GetPlayerIdByDeviceRsp) Reset() {
	*x = GetPlayerIdByDeviceRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_userrpc_userrpc_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPlayerIdByDeviceRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPlayerIdByDeviceRsp) ProtoMessage() {}

func (x *GetPlayerIdByDeviceRsp) ProtoReflect() protoreflect.Message {
	mi := &file_userrpc_userrpc_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPlayerIdByDeviceRsp.ProtoReflect.Descriptor instead.
func (*GetPlayerIdByDeviceRsp) Descriptor() ([]byte, []int) {
	return file_userrpc_userrpc_proto_rawDescGZIP(), []int{5}
}

func (x *GetPlayerIdByDeviceRsp) GetPlayerId() uint64 {
	if x != nil {
		return x.PlayerId
	}
	return 0
}

// 更新玩家信息请求
type UpdatePlayerInfoReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ProductId int32  `protobuf:"varint,1,opt,name=product_id,json=productId,proto3" json:"product_id,omitempty"` //产品id
	PlayerId  uint64 `protobuf:"varint,2,opt,name=player_id,json=playerId,proto3" json:"player_id,omitempty"`    //玩家id
	// string update_json = 2; //更新参数json
	UpdateMap map[string]string `protobuf:"bytes,3,rep,name=update_map,json=updateMap,proto3" json:"update_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *UpdatePlayerInfoReq) Reset() {
	*x = UpdatePlayerInfoReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_userrpc_userrpc_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdatePlayerInfoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdatePlayerInfoReq) ProtoMessage() {}

func (x *UpdatePlayerInfoReq) ProtoReflect() protoreflect.Message {
	mi := &file_userrpc_userrpc_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdatePlayerInfoReq.ProtoReflect.Descriptor instead.
func (*UpdatePlayerInfoReq) Descriptor() ([]byte, []int) {
	return file_userrpc_userrpc_proto_rawDescGZIP(), []int{6}
}

func (x *UpdatePlayerInfoReq) GetProductId() int32 {
	if x != nil {
		return x.ProductId
	}
	return 0
}

func (x *UpdatePlayerInfoReq) GetPlayerId() uint64 {
	if x != nil {
		return x.PlayerId
	}
	return 0
}

func (x *UpdatePlayerInfoReq) GetUpdateMap() map[string]string {
	if x != nil {
		return x.UpdateMap
	}
	return nil
}

// 更新玩家信息返回
type UpdatePlayerInfoRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret       *common.Result    `protobuf:"bytes,1,opt,name=ret,proto3" json:"ret,omitempty"`                            //更新结果
	PlayerId  uint64            `protobuf:"varint,2,opt,name=player_id,json=playerId,proto3" json:"player_id,omitempty"` //玩家id
	UpdateMap map[string]string `protobuf:"bytes,3,rep,name=update_map,json=updateMap,proto3" json:"update_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *UpdatePlayerInfoRsp) Reset() {
	*x = UpdatePlayerInfoRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_userrpc_userrpc_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdatePlayerInfoRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdatePlayerInfoRsp) ProtoMessage() {}

func (x *UpdatePlayerInfoRsp) ProtoReflect() protoreflect.Message {
	mi := &file_userrpc_userrpc_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdatePlayerInfoRsp.ProtoReflect.Descriptor instead.
func (*UpdatePlayerInfoRsp) Descriptor() ([]byte, []int) {
	return file_userrpc_userrpc_proto_rawDescGZIP(), []int{7}
}

func (x *UpdatePlayerInfoRsp) GetRet() *common.Result {
	if x != nil {
		return x.Ret
	}
	return nil
}

func (x *UpdatePlayerInfoRsp) GetPlayerId() uint64 {
	if x != nil {
		return x.PlayerId
	}
	return 0
}

func (x *UpdatePlayerInfoRsp) GetUpdateMap() map[string]string {
	if x != nil {
		return x.UpdateMap
	}
	return nil
}

// 查询玩家deviceInfo
type GetPlayerDeviceInfoReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ProductId int32  `protobuf:"varint,1,opt,name=product_id,json=productId,proto3" json:"product_id,omitempty"` //产品id
	PlayerId  uint64 `protobuf:"varint,2,opt,name=player_id,json=playerId,proto3" json:"player_id,omitempty"`    //玩家id
}

func (x *GetPlayerDeviceInfoReq) Reset() {
	*x = GetPlayerDeviceInfoReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_userrpc_userrpc_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPlayerDeviceInfoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPlayerDeviceInfoReq) ProtoMessage() {}

func (x *GetPlayerDeviceInfoReq) ProtoReflect() protoreflect.Message {
	mi := &file_userrpc_userrpc_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPlayerDeviceInfoReq.ProtoReflect.Descriptor instead.
func (*GetPlayerDeviceInfoReq) Descriptor() ([]byte, []int) {
	return file_userrpc_userrpc_proto_rawDescGZIP(), []int{8}
}

func (x *GetPlayerDeviceInfoReq) GetProductId() int32 {
	if x != nil {
		return x.ProductId
	}
	return 0
}

func (x *GetPlayerDeviceInfoReq) GetPlayerId() uint64 {
	if x != nil {
		return x.PlayerId
	}
	return 0
}

// 查询玩家deviceInfo返回
type GetPlayerDeviceInfoRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PlayerId   uint64             `protobuf:"varint,1,opt,name=player_id,json=playerId,proto3" json:"player_id,omitempty"`      //玩家id
	DeviceInfo *common.DeviceInfo `protobuf:"bytes,2,opt,name=device_info,json=deviceInfo,proto3" json:"device_info,omitempty"` //玩家设备信息
}

func (x *GetPlayerDeviceInfoRsp) Reset() {
	*x = GetPlayerDeviceInfoRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_userrpc_userrpc_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPlayerDeviceInfoRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPlayerDeviceInfoRsp) ProtoMessage() {}

func (x *GetPlayerDeviceInfoRsp) ProtoReflect() protoreflect.Message {
	mi := &file_userrpc_userrpc_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPlayerDeviceInfoRsp.ProtoReflect.Descriptor instead.
func (*GetPlayerDeviceInfoRsp) Descriptor() ([]byte, []int) {
	return file_userrpc_userrpc_proto_rawDescGZIP(), []int{9}
}

func (x *GetPlayerDeviceInfoRsp) GetPlayerId() uint64 {
	if x != nil {
		return x.PlayerId
	}
	return 0
}

func (x *GetPlayerDeviceInfoRsp) GetDeviceInfo() *common.DeviceInfo {
	if x != nil {
		return x.DeviceInfo
	}
	return nil
}

// 根据账号查询玩家id
type GetPlayerIdByAccountReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ProductId   int32               `protobuf:"varint,1,opt,name=product_id,json=productId,proto3" json:"product_id,omitempty"`      //产品id
	AccountInfo *common.AccountInfo `protobuf:"bytes,2,opt,name=account_info,json=accountInfo,proto3" json:"account_info,omitempty"` //账号信息
}

func (x *GetPlayerIdByAccountReq) Reset() {
	*x = GetPlayerIdByAccountReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_userrpc_userrpc_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPlayerIdByAccountReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPlayerIdByAccountReq) ProtoMessage() {}

func (x *GetPlayerIdByAccountReq) ProtoReflect() protoreflect.Message {
	mi := &file_userrpc_userrpc_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPlayerIdByAccountReq.ProtoReflect.Descriptor instead.
func (*GetPlayerIdByAccountReq) Descriptor() ([]byte, []int) {
	return file_userrpc_userrpc_proto_rawDescGZIP(), []int{10}
}

func (x *GetPlayerIdByAccountReq) GetProductId() int32 {
	if x != nil {
		return x.ProductId
	}
	return 0
}

func (x *GetPlayerIdByAccountReq) GetAccountInfo() *common.AccountInfo {
	if x != nil {
		return x.AccountInfo
	}
	return nil
}

// 根据账号查询玩家id返回
type GetPlayerIdByAccountRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PlayerId uint64 `protobuf:"varint,1,opt,name=player_id,json=playerId,proto3" json:"player_id,omitempty"` //玩家id
}

func (x *GetPlayerIdByAccountRsp) Reset() {
	*x = GetPlayerIdByAccountRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_userrpc_userrpc_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPlayerIdByAccountRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPlayerIdByAccountRsp) ProtoMessage() {}

func (x *GetPlayerIdByAccountRsp) ProtoReflect() protoreflect.Message {
	mi := &file_userrpc_userrpc_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPlayerIdByAccountRsp.ProtoReflect.Descriptor instead.
func (*GetPlayerIdByAccountRsp) Descriptor() ([]byte, []int) {
	return file_userrpc_userrpc_proto_rawDescGZIP(), []int{11}
}

func (x *GetPlayerIdByAccountRsp) GetPlayerId() uint64 {
	if x != nil {
		return x.PlayerId
	}
	return 0
}

// 注销账号
type DeleteAccountReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ProductId int32  `protobuf:"varint,1,opt,name=product_id,json=productId,proto3" json:"product_id,omitempty"` //产品id
	PlayerId  uint64 `protobuf:"varint,2,opt,name=player_id,json=playerId,proto3" json:"player_id,omitempty"`    //玩家id
}

func (x *DeleteAccountReq) Reset() {
	*x = DeleteAccountReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_userrpc_userrpc_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteAccountReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteAccountReq) ProtoMessage() {}

func (x *DeleteAccountReq) ProtoReflect() protoreflect.Message {
	mi := &file_userrpc_userrpc_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteAccountReq.ProtoReflect.Descriptor instead.
func (*DeleteAccountReq) Descriptor() ([]byte, []int) {
	return file_userrpc_userrpc_proto_rawDescGZIP(), []int{12}
}

func (x *DeleteAccountReq) GetProductId() int32 {
	if x != nil {
		return x.ProductId
	}
	return 0
}

func (x *DeleteAccountReq) GetPlayerId() uint64 {
	if x != nil {
		return x.PlayerId
	}
	return 0
}

// 注销账号
type DeleteAccountRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret *common.Result `protobuf:"bytes,1,opt,name=ret,proto3" json:"ret,omitempty"` //删除结果
}

func (x *DeleteAccountRsp) Reset() {
	*x = DeleteAccountRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_userrpc_userrpc_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteAccountRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteAccountRsp) ProtoMessage() {}

func (x *DeleteAccountRsp) ProtoReflect() protoreflect.Message {
	mi := &file_userrpc_userrpc_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteAccountRsp.ProtoReflect.Descriptor instead.
func (*DeleteAccountRsp) Descriptor() ([]byte, []int) {
	return file_userrpc_userrpc_proto_rawDescGZIP(), []int{13}
}

func (x *DeleteAccountRsp) GetRet() *common.Result {
	if x != nil {
		return x.Ret
	}
	return nil
}

// 查询实名认证信息请求
type RealNameAuthQueryReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ProductId int32  `protobuf:"varint,1,opt,name=product_id,json=productId,proto3" json:"product_id,omitempty"` //产品id
	PlayerId  uint64 `protobuf:"varint,2,opt,name=player_id,json=playerId,proto3" json:"player_id,omitempty"`    //玩家id
}

func (x *RealNameAuthQueryReq) Reset() {
	*x = RealNameAuthQueryReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_userrpc_userrpc_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RealNameAuthQueryReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RealNameAuthQueryReq) ProtoMessage() {}

func (x *RealNameAuthQueryReq) ProtoReflect() protoreflect.Message {
	mi := &file_userrpc_userrpc_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RealNameAuthQueryReq.ProtoReflect.Descriptor instead.
func (*RealNameAuthQueryReq) Descriptor() ([]byte, []int) {
	return file_userrpc_userrpc_proto_rawDescGZIP(), []int{14}
}

func (x *RealNameAuthQueryReq) GetProductId() int32 {
	if x != nil {
		return x.ProductId
	}
	return 0
}

func (x *RealNameAuthQueryReq) GetPlayerId() uint64 {
	if x != nil {
		return x.PlayerId
	}
	return 0
}

// 查询实名认证信息返回
type RealNameAuthQueryRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret        *common.Result `protobuf:"bytes,1,opt,name=ret,proto3" json:"ret,omitempty"`                                    //实名认证结果
	IsRealName bool           `protobuf:"varint,2,opt,name=is_real_name,json=isRealName,proto3" json:"is_real_name,omitempty"` //是否实名认证
}

func (x *RealNameAuthQueryRsp) Reset() {
	*x = RealNameAuthQueryRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_userrpc_userrpc_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RealNameAuthQueryRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RealNameAuthQueryRsp) ProtoMessage() {}

func (x *RealNameAuthQueryRsp) ProtoReflect() protoreflect.Message {
	mi := &file_userrpc_userrpc_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RealNameAuthQueryRsp.ProtoReflect.Descriptor instead.
func (*RealNameAuthQueryRsp) Descriptor() ([]byte, []int) {
	return file_userrpc_userrpc_proto_rawDescGZIP(), []int{15}
}

func (x *RealNameAuthQueryRsp) GetRet() *common.Result {
	if x != nil {
		return x.Ret
	}
	return nil
}

func (x *RealNameAuthQueryRsp) GetIsRealName() bool {
	if x != nil {
		return x.IsRealName
	}
	return false
}

// 更新实名认证信息请求
type RealNameAuthReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AuthInfo *common.PlayerRealNameAuth `protobuf:"bytes,1,opt,name=auth_info,json=authInfo,proto3" json:"auth_info,omitempty"` //实名认证信息
}

func (x *RealNameAuthReq) Reset() {
	*x = RealNameAuthReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_userrpc_userrpc_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RealNameAuthReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RealNameAuthReq) ProtoMessage() {}

func (x *RealNameAuthReq) ProtoReflect() protoreflect.Message {
	mi := &file_userrpc_userrpc_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RealNameAuthReq.ProtoReflect.Descriptor instead.
func (*RealNameAuthReq) Descriptor() ([]byte, []int) {
	return file_userrpc_userrpc_proto_rawDescGZIP(), []int{16}
}

func (x *RealNameAuthReq) GetAuthInfo() *common.PlayerRealNameAuth {
	if x != nil {
		return x.AuthInfo
	}
	return nil
}

// 更新实名认证信息返回
type RealNameAuthRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret *common.Result `protobuf:"bytes,1,opt,name=ret,proto3" json:"ret,omitempty"` //实名认证结果
}

func (x *RealNameAuthRsp) Reset() {
	*x = RealNameAuthRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_userrpc_userrpc_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RealNameAuthRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RealNameAuthRsp) ProtoMessage() {}

func (x *RealNameAuthRsp) ProtoReflect() protoreflect.Message {
	mi := &file_userrpc_userrpc_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RealNameAuthRsp.ProtoReflect.Descriptor instead.
func (*RealNameAuthRsp) Descriptor() ([]byte, []int) {
	return file_userrpc_userrpc_proto_rawDescGZIP(), []int{17}
}

func (x *RealNameAuthRsp) GetRet() *common.Result {
	if x != nil {
		return x.Ret
	}
	return nil
}

// 用户年龄短查询
type PlayerAgeQueryReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ProductId int32  `protobuf:"varint,1,opt,name=product_id,json=productId,proto3" json:"product_id,omitempty"` //产品id
	PlayerId  uint64 `protobuf:"varint,2,opt,name=player_id,json=playerId,proto3" json:"player_id,omitempty"`    //玩家id
}

func (x *PlayerAgeQueryReq) Reset() {
	*x = PlayerAgeQueryReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_userrpc_userrpc_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PlayerAgeQueryReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PlayerAgeQueryReq) ProtoMessage() {}

func (x *PlayerAgeQueryReq) ProtoReflect() protoreflect.Message {
	mi := &file_userrpc_userrpc_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PlayerAgeQueryReq.ProtoReflect.Descriptor instead.
func (*PlayerAgeQueryReq) Descriptor() ([]byte, []int) {
	return file_userrpc_userrpc_proto_rawDescGZIP(), []int{18}
}

func (x *PlayerAgeQueryReq) GetProductId() int32 {
	if x != nil {
		return x.ProductId
	}
	return 0
}

func (x *PlayerAgeQueryReq) GetPlayerId() uint64 {
	if x != nil {
		return x.PlayerId
	}
	return 0
}

// 用户年龄段查询
type PlayerAgeQueryRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret *common.Result  `protobuf:"bytes,1,opt,name=ret,proto3" json:"ret,omitempty"`                       //
	Age common.USER_AGE `protobuf:"varint,2,opt,name=age,proto3,enum=common.USER_AGE" json:"age,omitempty"` // 年龄段定义
}

func (x *PlayerAgeQueryRsp) Reset() {
	*x = PlayerAgeQueryRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_userrpc_userrpc_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PlayerAgeQueryRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PlayerAgeQueryRsp) ProtoMessage() {}

func (x *PlayerAgeQueryRsp) ProtoReflect() protoreflect.Message {
	mi := &file_userrpc_userrpc_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PlayerAgeQueryRsp.ProtoReflect.Descriptor instead.
func (*PlayerAgeQueryRsp) Descriptor() ([]byte, []int) {
	return file_userrpc_userrpc_proto_rawDescGZIP(), []int{19}
}

func (x *PlayerAgeQueryRsp) GetRet() *common.Result {
	if x != nil {
		return x.Ret
	}
	return nil
}

func (x *PlayerAgeQueryRsp) GetAge() common.USER_AGE {
	if x != nil {
		return x.Age
	}
	return common.USER_AGE(0)
}

// 批量多个玩家查询
type PlayerMultiQueryReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ProductId int32    `protobuf:"varint,1,opt,name=product_id,json=productId,proto3" json:"product_id,omitempty"`
	PlayerId  []uint64 `protobuf:"varint,2,rep,packed,name=player_id,json=playerId,proto3" json:"player_id,omitempty"` //玩家id
}

func (x *PlayerMultiQueryReq) Reset() {
	*x = PlayerMultiQueryReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_userrpc_userrpc_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PlayerMultiQueryReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PlayerMultiQueryReq) ProtoMessage() {}

func (x *PlayerMultiQueryReq) ProtoReflect() protoreflect.Message {
	mi := &file_userrpc_userrpc_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PlayerMultiQueryReq.ProtoReflect.Descriptor instead.
func (*PlayerMultiQueryReq) Descriptor() ([]byte, []int) {
	return file_userrpc_userrpc_proto_rawDescGZIP(), []int{20}
}

func (x *PlayerMultiQueryReq) GetProductId() int32 {
	if x != nil {
		return x.ProductId
	}
	return 0
}

func (x *PlayerMultiQueryReq) GetPlayerId() []uint64 {
	if x != nil {
		return x.PlayerId
	}
	return nil
}

// 批量多个玩家查询
type PlayerMultiQueryRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret        *common.Result                  `protobuf:"bytes,1,opt,name=ret,proto3" json:"ret,omitempty"`                                                                                                                          //
	PlayerInfo map[uint64]*common.RichUserInfo `protobuf:"bytes,2,rep,name=player_info,json=playerInfo,proto3" json:"player_info,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` // 玩家信息
}

func (x *PlayerMultiQueryRsp) Reset() {
	*x = PlayerMultiQueryRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_userrpc_userrpc_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PlayerMultiQueryRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PlayerMultiQueryRsp) ProtoMessage() {}

func (x *PlayerMultiQueryRsp) ProtoReflect() protoreflect.Message {
	mi := &file_userrpc_userrpc_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PlayerMultiQueryRsp.ProtoReflect.Descriptor instead.
func (*PlayerMultiQueryRsp) Descriptor() ([]byte, []int) {
	return file_userrpc_userrpc_proto_rawDescGZIP(), []int{21}
}

func (x *PlayerMultiQueryRsp) GetRet() *common.Result {
	if x != nil {
		return x.Ret
	}
	return nil
}

func (x *PlayerMultiQueryRsp) GetPlayerInfo() map[uint64]*common.RichUserInfo {
	if x != nil {
		return x.PlayerInfo
	}
	return nil
}

// 更新玩家登录请求
type UpdateLoginReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ProductId  int32              `protobuf:"varint,1,opt,name=product_id,json=productId,proto3" json:"product_id,omitempty"`   //产品id
	PlayerId   uint64             `protobuf:"varint,2,opt,name=player_id,json=playerId,proto3" json:"player_id,omitempty"`      //玩家id
	DeviceInfo *common.DeviceInfo `protobuf:"bytes,3,opt,name=device_info,json=deviceInfo,proto3" json:"device_info,omitempty"` // 设备信息
}

func (x *UpdateLoginReq) Reset() {
	*x = UpdateLoginReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_userrpc_userrpc_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateLoginReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateLoginReq) ProtoMessage() {}

func (x *UpdateLoginReq) ProtoReflect() protoreflect.Message {
	mi := &file_userrpc_userrpc_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateLoginReq.ProtoReflect.Descriptor instead.
func (*UpdateLoginReq) Descriptor() ([]byte, []int) {
	return file_userrpc_userrpc_proto_rawDescGZIP(), []int{22}
}

func (x *UpdateLoginReq) GetProductId() int32 {
	if x != nil {
		return x.ProductId
	}
	return 0
}

func (x *UpdateLoginReq) GetPlayerId() uint64 {
	if x != nil {
		return x.PlayerId
	}
	return 0
}

func (x *UpdateLoginReq) GetDeviceInfo() *common.DeviceInfo {
	if x != nil {
		return x.DeviceInfo
	}
	return nil
}

// 更新玩家登录返回
type UpdateLoginRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret *common.Result `protobuf:"bytes,1,opt,name=ret,proto3" json:"ret,omitempty"` //更新结果
}

func (x *UpdateLoginRsp) Reset() {
	*x = UpdateLoginRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_userrpc_userrpc_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateLoginRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateLoginRsp) ProtoMessage() {}

func (x *UpdateLoginRsp) ProtoReflect() protoreflect.Message {
	mi := &file_userrpc_userrpc_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateLoginRsp.ProtoReflect.Descriptor instead.
func (*UpdateLoginRsp) Descriptor() ([]byte, []int) {
	return file_userrpc_userrpc_proto_rawDescGZIP(), []int{23}
}

func (x *UpdateLoginRsp) GetRet() *common.Result {
	if x != nil {
		return x.Ret
	}
	return nil
}

// 批量查询玩家用户信息请求
type BatchPlayerInfoReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ProductId  int32                 `protobuf:"varint,1,opt,name=product_id,json=productId,proto3" json:"product_id,omitempty"` //产品id
	PlayerId   uint64                `protobuf:"varint,2,opt,name=player_id,json=playerId,proto3" json:"player_id,omitempty"`    //玩家id
	Pagination *common.PaginationReq `protobuf:"bytes,3,opt,name=pagination,proto3" json:"pagination,omitempty"`
}

func (x *BatchPlayerInfoReq) Reset() {
	*x = BatchPlayerInfoReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_userrpc_userrpc_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchPlayerInfoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchPlayerInfoReq) ProtoMessage() {}

func (x *BatchPlayerInfoReq) ProtoReflect() protoreflect.Message {
	mi := &file_userrpc_userrpc_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchPlayerInfoReq.ProtoReflect.Descriptor instead.
func (*BatchPlayerInfoReq) Descriptor() ([]byte, []int) {
	return file_userrpc_userrpc_proto_rawDescGZIP(), []int{24}
}

func (x *BatchPlayerInfoReq) GetProductId() int32 {
	if x != nil {
		return x.ProductId
	}
	return 0
}

func (x *BatchPlayerInfoReq) GetPlayerId() uint64 {
	if x != nil {
		return x.PlayerId
	}
	return 0
}

func (x *BatchPlayerInfoReq) GetPagination() *common.PaginationReq {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// 批量查询玩家用户信息返回
type BatchPlayerInfoRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret        *common.Result         `protobuf:"bytes,1,opt,name=ret,proto3" json:"ret,omitempty"`
	PlayerInfo []*common.RichUserInfo `protobuf:"bytes,2,rep,name=player_info,json=playerInfo,proto3" json:"player_info,omitempty"`
	Count      int32                  `protobuf:"varint,3,opt,name=count,proto3" json:"count,omitempty"`
}

func (x *BatchPlayerInfoRsp) Reset() {
	*x = BatchPlayerInfoRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_userrpc_userrpc_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchPlayerInfoRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchPlayerInfoRsp) ProtoMessage() {}

func (x *BatchPlayerInfoRsp) ProtoReflect() protoreflect.Message {
	mi := &file_userrpc_userrpc_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchPlayerInfoRsp.ProtoReflect.Descriptor instead.
func (*BatchPlayerInfoRsp) Descriptor() ([]byte, []int) {
	return file_userrpc_userrpc_proto_rawDescGZIP(), []int{25}
}

func (x *BatchPlayerInfoRsp) GetRet() *common.Result {
	if x != nil {
		return x.Ret
	}
	return nil
}

func (x *BatchPlayerInfoRsp) GetPlayerInfo() []*common.RichUserInfo {
	if x != nil {
		return x.PlayerInfo
	}
	return nil
}

func (x *BatchPlayerInfoRsp) GetCount() int32 {
	if x != nil {
		return x.Count
	}
	return 0
}

// 根据openId查询玩家id请求
type GetPlayerIdByOpenIdReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ProductId int32             `protobuf:"varint,1,opt,name=product_id,json=productId,proto3" json:"product_id,omitempty"`                        //产品id
	LoginType common.LOGIN_TYPE `protobuf:"varint,2,opt,name=login_type,json=loginType,proto3,enum=common.LOGIN_TYPE" json:"login_type,omitempty"` //登录类型
	OpenId    string            `protobuf:"bytes,3,opt,name=open_id,json=openId,proto3" json:"open_id,omitempty"`                                  //open id
}

func (x *GetPlayerIdByOpenIdReq) Reset() {
	*x = GetPlayerIdByOpenIdReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_userrpc_userrpc_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPlayerIdByOpenIdReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPlayerIdByOpenIdReq) ProtoMessage() {}

func (x *GetPlayerIdByOpenIdReq) ProtoReflect() protoreflect.Message {
	mi := &file_userrpc_userrpc_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPlayerIdByOpenIdReq.ProtoReflect.Descriptor instead.
func (*GetPlayerIdByOpenIdReq) Descriptor() ([]byte, []int) {
	return file_userrpc_userrpc_proto_rawDescGZIP(), []int{26}
}

func (x *GetPlayerIdByOpenIdReq) GetProductId() int32 {
	if x != nil {
		return x.ProductId
	}
	return 0
}

func (x *GetPlayerIdByOpenIdReq) GetLoginType() common.LOGIN_TYPE {
	if x != nil {
		return x.LoginType
	}
	return common.LOGIN_TYPE(0)
}

func (x *GetPlayerIdByOpenIdReq) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

// 根据openId查询玩家id返回
type GetPlayerIdByOpenIdRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PlayerId uint64 `protobuf:"varint,1,opt,name=player_id,json=playerId,proto3" json:"player_id,omitempty"` //玩家id
}

func (x *GetPlayerIdByOpenIdRsp) Reset() {
	*x = GetPlayerIdByOpenIdRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_userrpc_userrpc_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPlayerIdByOpenIdRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPlayerIdByOpenIdRsp) ProtoMessage() {}

func (x *GetPlayerIdByOpenIdRsp) ProtoReflect() protoreflect.Message {
	mi := &file_userrpc_userrpc_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPlayerIdByOpenIdRsp.ProtoReflect.Descriptor instead.
func (*GetPlayerIdByOpenIdRsp) Descriptor() ([]byte, []int) {
	return file_userrpc_userrpc_proto_rawDescGZIP(), []int{27}
}

func (x *GetPlayerIdByOpenIdRsp) GetPlayerId() uint64 {
	if x != nil {
		return x.PlayerId
	}
	return 0
}

// 查询玩家拓展信息请求
type QueryPlayerExtendInfoReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ProductId int32  `protobuf:"varint,1,opt,name=product_id,json=productId,proto3" json:"product_id,omitempty"` //产品id
	PlayerId  uint64 `protobuf:"varint,2,opt,name=player_id,json=playerId,proto3" json:"player_id,omitempty"`    //玩家id
}

func (x *QueryPlayerExtendInfoReq) Reset() {
	*x = QueryPlayerExtendInfoReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_userrpc_userrpc_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryPlayerExtendInfoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryPlayerExtendInfoReq) ProtoMessage() {}

func (x *QueryPlayerExtendInfoReq) ProtoReflect() protoreflect.Message {
	mi := &file_userrpc_userrpc_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryPlayerExtendInfoReq.ProtoReflect.Descriptor instead.
func (*QueryPlayerExtendInfoReq) Descriptor() ([]byte, []int) {
	return file_userrpc_userrpc_proto_rawDescGZIP(), []int{28}
}

func (x *QueryPlayerExtendInfoReq) GetProductId() int32 {
	if x != nil {
		return x.ProductId
	}
	return 0
}

func (x *QueryPlayerExtendInfoReq) GetPlayerId() uint64 {
	if x != nil {
		return x.PlayerId
	}
	return 0
}

// 查询玩家拓展信息返回
type QueryPlayerExtendInfoRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret        *common.Result         `protobuf:"bytes,1,opt,name=ret,proto3" json:"ret,omitempty"`                                 //查询结果
	ExtendInfo *common.ExtendUserInfo `protobuf:"bytes,2,opt,name=extend_info,json=extendInfo,proto3" json:"extend_info,omitempty"` //玩家拓展信息
}

func (x *QueryPlayerExtendInfoRsp) Reset() {
	*x = QueryPlayerExtendInfoRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_userrpc_userrpc_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryPlayerExtendInfoRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryPlayerExtendInfoRsp) ProtoMessage() {}

func (x *QueryPlayerExtendInfoRsp) ProtoReflect() protoreflect.Message {
	mi := &file_userrpc_userrpc_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryPlayerExtendInfoRsp.ProtoReflect.Descriptor instead.
func (*QueryPlayerExtendInfoRsp) Descriptor() ([]byte, []int) {
	return file_userrpc_userrpc_proto_rawDescGZIP(), []int{29}
}

func (x *QueryPlayerExtendInfoRsp) GetRet() *common.Result {
	if x != nil {
		return x.Ret
	}
	return nil
}

func (x *QueryPlayerExtendInfoRsp) GetExtendInfo() *common.ExtendUserInfo {
	if x != nil {
		return x.ExtendInfo
	}
	return nil
}

// 更新玩家拓展信息
type UpdatePlayerExtendInfoReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ProductId  int32                  `protobuf:"varint,1,opt,name=product_id,json=productId,proto3" json:"product_id,omitempty"`   //产品id
	PlayerId   uint64                 `protobuf:"varint,2,opt,name=player_id,json=playerId,proto3" json:"player_id,omitempty"`      //玩家id
	ExtendInfo *common.ExtendUserInfo `protobuf:"bytes,3,opt,name=extend_info,json=extendInfo,proto3" json:"extend_info,omitempty"` //玩家拓展信息
}

func (x *UpdatePlayerExtendInfoReq) Reset() {
	*x = UpdatePlayerExtendInfoReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_userrpc_userrpc_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdatePlayerExtendInfoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdatePlayerExtendInfoReq) ProtoMessage() {}

func (x *UpdatePlayerExtendInfoReq) ProtoReflect() protoreflect.Message {
	mi := &file_userrpc_userrpc_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdatePlayerExtendInfoReq.ProtoReflect.Descriptor instead.
func (*UpdatePlayerExtendInfoReq) Descriptor() ([]byte, []int) {
	return file_userrpc_userrpc_proto_rawDescGZIP(), []int{30}
}

func (x *UpdatePlayerExtendInfoReq) GetProductId() int32 {
	if x != nil {
		return x.ProductId
	}
	return 0
}

func (x *UpdatePlayerExtendInfoReq) GetPlayerId() uint64 {
	if x != nil {
		return x.PlayerId
	}
	return 0
}

func (x *UpdatePlayerExtendInfoReq) GetExtendInfo() *common.ExtendUserInfo {
	if x != nil {
		return x.ExtendInfo
	}
	return nil
}

// 更新玩家拓展信息返回
type UpdatePlayerExtendInfoRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret *common.Result `protobuf:"bytes,1,opt,name=ret,proto3" json:"ret,omitempty"` //更新结果
}

func (x *UpdatePlayerExtendInfoRsp) Reset() {
	*x = UpdatePlayerExtendInfoRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_userrpc_userrpc_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdatePlayerExtendInfoRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdatePlayerExtendInfoRsp) ProtoMessage() {}

func (x *UpdatePlayerExtendInfoRsp) ProtoReflect() protoreflect.Message {
	mi := &file_userrpc_userrpc_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdatePlayerExtendInfoRsp.ProtoReflect.Descriptor instead.
func (*UpdatePlayerExtendInfoRsp) Descriptor() ([]byte, []int) {
	return file_userrpc_userrpc_proto_rawDescGZIP(), []int{31}
}

func (x *UpdatePlayerExtendInfoRsp) GetRet() *common.Result {
	if x != nil {
		return x.Ret
	}
	return nil
}

// 批量查询玩家简要信息请求
type BatchPlayerBriefInfoReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ProductId  int32    `protobuf:"varint,1,opt,name=product_id,json=productId,proto3" json:"product_id,omitempty"`           //产品id
	PlayerList []uint64 `protobuf:"varint,2,rep,packed,name=player_list,json=playerList,proto3" json:"player_list,omitempty"` //玩家id列表
}

func (x *BatchPlayerBriefInfoReq) Reset() {
	*x = BatchPlayerBriefInfoReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_userrpc_userrpc_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchPlayerBriefInfoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchPlayerBriefInfoReq) ProtoMessage() {}

func (x *BatchPlayerBriefInfoReq) ProtoReflect() protoreflect.Message {
	mi := &file_userrpc_userrpc_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchPlayerBriefInfoReq.ProtoReflect.Descriptor instead.
func (*BatchPlayerBriefInfoReq) Descriptor() ([]byte, []int) {
	return file_userrpc_userrpc_proto_rawDescGZIP(), []int{32}
}

func (x *BatchPlayerBriefInfoReq) GetProductId() int32 {
	if x != nil {
		return x.ProductId
	}
	return 0
}

func (x *BatchPlayerBriefInfoReq) GetPlayerList() []uint64 {
	if x != nil {
		return x.PlayerList
	}
	return nil
}

// 批量查询玩家简要信息返回
type BatchPlayerBriefInfoRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret        *common.Result          `protobuf:"bytes,1,opt,name=ret,proto3" json:"ret,omitempty"`                                 //查询结果
	PlayerInfo []*common.BriefUserInfo `protobuf:"bytes,2,rep,name=player_info,json=playerInfo,proto3" json:"player_info,omitempty"` //玩家信息列表
}

func (x *BatchPlayerBriefInfoRsp) Reset() {
	*x = BatchPlayerBriefInfoRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_userrpc_userrpc_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchPlayerBriefInfoRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchPlayerBriefInfoRsp) ProtoMessage() {}

func (x *BatchPlayerBriefInfoRsp) ProtoReflect() protoreflect.Message {
	mi := &file_userrpc_userrpc_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchPlayerBriefInfoRsp.ProtoReflect.Descriptor instead.
func (*BatchPlayerBriefInfoRsp) Descriptor() ([]byte, []int) {
	return file_userrpc_userrpc_proto_rawDescGZIP(), []int{33}
}

func (x *BatchPlayerBriefInfoRsp) GetRet() *common.Result {
	if x != nil {
		return x.Ret
	}
	return nil
}

func (x *BatchPlayerBriefInfoRsp) GetPlayerInfo() []*common.BriefUserInfo {
	if x != nil {
		return x.PlayerInfo
	}
	return nil
}

var File_userrpc_userrpc_proto protoreflect.FileDescriptor

var file_userrpc_userrpc_proto_rawDesc = []byte{
	0x0a, 0x15, 0x75, 0x73, 0x65, 0x72, 0x72, 0x70, 0x63, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x72, 0x70,
	0x63, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x07, 0x75, 0x73, 0x65, 0x72, 0x52, 0x70, 0x63,
	0x1a, 0x0c, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x0a,
	0x65, 0x6e, 0x75, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x0c, 0x65, 0x72, 0x72, 0x6f,
	0x72, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x0e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e,
	0x61, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x8a, 0x04, 0x0a, 0x0f, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x52, 0x65, 0x71, 0x12, 0x25, 0x0a, 0x0e,
	0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x56, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74,
	0x49, 0x64, 0x12, 0x33, 0x0a, 0x0a, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x69, 0x64,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x43, 0x48, 0x41, 0x4e, 0x4e, 0x45, 0x4c, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x52, 0x09, 0x63, 0x68,
	0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x49, 0x64, 0x12, 0x33, 0x0a, 0x0b, 0x64, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x0a, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1f, 0x0a, 0x0b,
	0x74, 0x68, 0x69, 0x72, 0x64, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x74, 0x68, 0x69, 0x72, 0x64, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x2e, 0x0a,
	0x07, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4e, 0x45, 0x54, 0x57, 0x4f, 0x52, 0x4b, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x52, 0x07, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x12, 0x1f, 0x0a,
	0x0b, 0x62, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x62, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x31,
	0x0a, 0x08, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x4c, 0x41, 0x54, 0x46, 0x4f,
	0x52, 0x4d, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x52, 0x08, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72,
	0x6d, 0x12, 0x36, 0x0a, 0x0c, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x6e, 0x66,
	0x6f, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0b, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x33, 0x0a, 0x0b, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x12,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4c, 0x4f, 0x47, 0x49, 0x4e, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x35,
	0x0a, 0x0a, 0x74, 0x68, 0x69, 0x72, 0x64, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x0c, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x16, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x68, 0x69, 0x72,
	0x64, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x09, 0x74, 0x68, 0x69, 0x72,
	0x64, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x6a, 0x0a, 0x0f, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50,
	0x6c, 0x61, 0x79, 0x65, 0x72, 0x52, 0x73, 0x70, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x6c, 0x61, 0x79,
	0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x70, 0x6c, 0x61,
	0x79, 0x65, 0x72, 0x49, 0x64, 0x12, 0x3a, 0x0a, 0x0e, 0x72, 0x69, 0x63, 0x68, 0x5f, 0x75, 0x73,
	0x65, 0x72, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x69, 0x63, 0x68, 0x55, 0x73, 0x65, 0x72, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x0c, 0x72, 0x69, 0x63, 0x68, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66,
	0x6f, 0x22, 0x4e, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x65, 0x71, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x64, 0x75,
	0x63, 0x74, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49,
	0x64, 0x22, 0x6b, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x73, 0x70, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72,
	0x49, 0x64, 0x12, 0x3a, 0x0a, 0x0e, 0x72, 0x69, 0x63, 0x68, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x5f,
	0x69, 0x6e, 0x66, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x69, 0x63, 0x68, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x0c, 0x72, 0x69, 0x63, 0x68, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x58,
	0x0a, 0x16, 0x47, 0x65, 0x74, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x64, 0x42, 0x79, 0x44,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x71, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x64,
	0x75, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x70, 0x72,
	0x6f, 0x64, 0x75, 0x63, 0x74, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x64, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x64, 0x65,
	0x76, 0x69, 0x63, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x22, 0x35, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x50,
	0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x64, 0x42, 0x79, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52,
	0x73, 0x70, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x64, 0x22,
	0xdb, 0x01, 0x0a, 0x13, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x64, 0x75,
	0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x70, 0x72, 0x6f,
	0x64, 0x75, 0x63, 0x74, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x70, 0x6c, 0x61, 0x79, 0x65,
	0x72, 0x49, 0x64, 0x12, 0x4a, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x6d, 0x61,
	0x70, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x52, 0x70,
	0x63, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x65, 0x71, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4d, 0x61, 0x70, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4d, 0x61, 0x70, 0x1a,
	0x3c, 0x0a, 0x0e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xde, 0x01,
	0x0a, 0x13, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x73, 0x70, 0x12, 0x20, 0x0a, 0x03, 0x72, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x52, 0x03, 0x72, 0x65, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x6c, 0x61, 0x79, 0x65,
	0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x70, 0x6c, 0x61, 0x79,
	0x65, 0x72, 0x49, 0x64, 0x12, 0x4a, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x6d,
	0x61, 0x70, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x52,
	0x70, 0x63, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x73, 0x70, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4d, 0x61, 0x70,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4d, 0x61, 0x70,
	0x1a, 0x3c, 0x0a, 0x0e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x54,
	0x0a, 0x16, 0x47, 0x65, 0x74, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x44, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x64,
	0x75, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x70, 0x72,
	0x6f, 0x64, 0x75, 0x63, 0x74, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x6c, 0x61, 0x79, 0x65,
	0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x70, 0x6c, 0x61, 0x79,
	0x65, 0x72, 0x49, 0x64, 0x22, 0x6a, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x50, 0x6c, 0x61, 0x79, 0x65,
	0x72, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x73, 0x70, 0x12, 0x1b,
	0x0a, 0x09, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x08, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x64, 0x12, 0x33, 0x0a, 0x0b, 0x64,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x12, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0a, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f,
	0x22, 0x70, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x64, 0x42,
	0x79, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x12, 0x1d, 0x0a, 0x0a, 0x70,
	0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x09, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x49, 0x64, 0x12, 0x36, 0x0a, 0x0c, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x13, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0b, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x6e,
	0x66, 0x6f, 0x22, 0x36, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49,
	0x64, 0x42, 0x79, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x73, 0x70, 0x12, 0x1b, 0x0a,
	0x09, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x08, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x64, 0x22, 0x4e, 0x0a, 0x10, 0x44, 0x65,
	0x6c, 0x65, 0x74, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x12, 0x1d,
	0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x49, 0x64, 0x12, 0x1b, 0x0a,
	0x09, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x08, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x64, 0x22, 0x34, 0x0a, 0x10, 0x44, 0x65,
	0x6c, 0x65, 0x74, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x73, 0x70, 0x12, 0x20,
	0x0a, 0x03, 0x72, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x03, 0x72, 0x65, 0x74,
	0x22, 0x52, 0x0a, 0x14, 0x52, 0x65, 0x61, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x41, 0x75, 0x74, 0x68,
	0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x65, 0x71, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x64,
	0x75, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x70, 0x72,
	0x6f, 0x64, 0x75, 0x63, 0x74, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x6c, 0x61, 0x79, 0x65,
	0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x70, 0x6c, 0x61, 0x79,
	0x65, 0x72, 0x49, 0x64, 0x22, 0x5a, 0x0a, 0x14, 0x52, 0x65, 0x61, 0x6c, 0x4e, 0x61, 0x6d, 0x65,
	0x41, 0x75, 0x74, 0x68, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x73, 0x70, 0x12, 0x20, 0x0a, 0x03,
	0x72, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x03, 0x72, 0x65, 0x74, 0x12, 0x20,
	0x0a, 0x0c, 0x69, 0x73, 0x5f, 0x72, 0x65, 0x61, 0x6c, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x69, 0x73, 0x52, 0x65, 0x61, 0x6c, 0x4e, 0x61, 0x6d, 0x65,
	0x22, 0x4a, 0x0a, 0x0f, 0x52, 0x65, 0x61, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x41, 0x75, 0x74, 0x68,
	0x52, 0x65, 0x71, 0x12, 0x37, 0x0a, 0x09, 0x61, 0x75, 0x74, 0x68, 0x5f, 0x69, 0x6e, 0x66, 0x6f,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x52, 0x65, 0x61, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x41, 0x75,
	0x74, 0x68, 0x52, 0x08, 0x61, 0x75, 0x74, 0x68, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x33, 0x0a, 0x0f,
	0x52, 0x65, 0x61, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x41, 0x75, 0x74, 0x68, 0x52, 0x73, 0x70, 0x12,
	0x20, 0x0a, 0x03, 0x72, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x03, 0x72, 0x65,
	0x74, 0x22, 0x4f, 0x0a, 0x11, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x41, 0x67, 0x65, 0x51, 0x75,
	0x65, 0x72, 0x79, 0x52, 0x65, 0x71, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x64,
	0x75, 0x63, 0x74, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72,
	0x49, 0x64, 0x22, 0x59, 0x0a, 0x11, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x41, 0x67, 0x65, 0x51,
	0x75, 0x65, 0x72, 0x79, 0x52, 0x73, 0x70, 0x12, 0x20, 0x0a, 0x03, 0x72, 0x65, 0x74, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x52, 0x03, 0x72, 0x65, 0x74, 0x12, 0x22, 0x0a, 0x03, 0x61, 0x67, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x10, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x55, 0x53, 0x45, 0x52, 0x5f, 0x41, 0x47, 0x45, 0x52, 0x03, 0x61, 0x67, 0x65, 0x22, 0x51, 0x0a,
	0x13, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x4d, 0x75, 0x6c, 0x74, 0x69, 0x51, 0x75, 0x65, 0x72,
	0x79, 0x52, 0x65, 0x71, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63,
	0x74, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x04, 0x52, 0x08, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x64,
	0x22, 0xdb, 0x01, 0x0a, 0x13, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x4d, 0x75, 0x6c, 0x74, 0x69,
	0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x73, 0x70, 0x12, 0x20, 0x0a, 0x03, 0x72, 0x65, 0x74, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x03, 0x72, 0x65, 0x74, 0x12, 0x4d, 0x0a, 0x0b, 0x70, 0x6c,
	0x61, 0x79, 0x65, 0x72, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x2c, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x52, 0x70, 0x63, 0x2e, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72,
	0x4d, 0x75, 0x6c, 0x74, 0x69, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x73, 0x70, 0x2e, 0x50, 0x6c,
	0x61, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0a, 0x70,
	0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x1a, 0x53, 0x0a, 0x0f, 0x50, 0x6c, 0x61,
	0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03,
	0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x2a,
	0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x69, 0x63, 0x68, 0x55, 0x73, 0x65, 0x72, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x81,
	0x01, 0x0a, 0x0e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x52, 0x65,
	0x71, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x49, 0x64,
	0x12, 0x1b, 0x0a, 0x09, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x08, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x64, 0x12, 0x33, 0x0a,
	0x0b, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x12, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x44, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0a, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x6e,
	0x66, 0x6f, 0x22, 0x32, 0x0a, 0x0e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4c, 0x6f, 0x67, 0x69,
	0x6e, 0x52, 0x73, 0x70, 0x12, 0x20, 0x0a, 0x03, 0x72, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x52, 0x03, 0x72, 0x65, 0x74, 0x22, 0x87, 0x01, 0x0a, 0x12, 0x42, 0x61, 0x74, 0x63, 0x68,
	0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x12, 0x1d, 0x0a,
	0x0a, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09,
	0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x08, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x64, 0x12, 0x35, 0x0a, 0x0a, 0x70, 0x61, 0x67,
	0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x65, 0x71, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x22, 0x83, 0x01, 0x0a, 0x12, 0x42, 0x61, 0x74, 0x63, 0x68, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x73, 0x70, 0x12, 0x20, 0x0a, 0x03, 0x72, 0x65, 0x74, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x52, 0x03, 0x72, 0x65, 0x74, 0x12, 0x35, 0x0a, 0x0b, 0x70, 0x6c, 0x61,
	0x79, 0x65, 0x72, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x69, 0x63, 0x68, 0x55, 0x73, 0x65, 0x72,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0a, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x83, 0x01, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x50, 0x6c,
	0x61, 0x79, 0x65, 0x72, 0x49, 0x64, 0x42, 0x79, 0x4f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x52, 0x65,
	0x71, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x49, 0x64,
	0x12, 0x31, 0x0a, 0x0a, 0x6c, 0x6f, 0x67, 0x69, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x12, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4c, 0x4f,
	0x47, 0x49, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x52, 0x09, 0x6c, 0x6f, 0x67, 0x69, 0x6e, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x22, 0x35, 0x0a, 0x16,
	0x47, 0x65, 0x74, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x64, 0x42, 0x79, 0x4f, 0x70, 0x65,
	0x6e, 0x49, 0x64, 0x52, 0x73, 0x70, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x70, 0x6c, 0x61, 0x79, 0x65,
	0x72, 0x49, 0x64, 0x22, 0x56, 0x0a, 0x18, 0x51, 0x75, 0x65, 0x72, 0x79, 0x50, 0x6c, 0x61, 0x79,
	0x65, 0x72, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x12,
	0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x49, 0x64, 0x12, 0x1b,
	0x0a, 0x09, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x08, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x64, 0x22, 0x75, 0x0a, 0x18, 0x51,
	0x75, 0x65, 0x72, 0x79, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x64,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x73, 0x70, 0x12, 0x20, 0x0a, 0x03, 0x72, 0x65, 0x74, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x52, 0x03, 0x72, 0x65, 0x74, 0x12, 0x37, 0x0a, 0x0b, 0x65, 0x78, 0x74,
	0x65, 0x6e, 0x64, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x64, 0x55, 0x73,
	0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0a, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x64, 0x49, 0x6e,
	0x66, 0x6f, 0x22, 0x90, 0x01, 0x0a, 0x19, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x6c, 0x61,
	0x79, 0x65, 0x72, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71,
	0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x49, 0x64, 0x12,
	0x1b, 0x0a, 0x09, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x08, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x64, 0x12, 0x37, 0x0a, 0x0b,
	0x65, 0x78, 0x74, 0x65, 0x6e, 0x64, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x16, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x45, 0x78, 0x74, 0x65, 0x6e,
	0x64, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0a, 0x65, 0x78, 0x74, 0x65, 0x6e,
	0x64, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x3d, 0x0a, 0x19, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50,
	0x6c, 0x61, 0x79, 0x65, 0x72, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x52,
	0x73, 0x70, 0x12, 0x20, 0x0a, 0x03, 0x72, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x0e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52,
	0x03, 0x72, 0x65, 0x74, 0x22, 0x59, 0x0a, 0x17, 0x42, 0x61, 0x74, 0x63, 0x68, 0x50, 0x6c, 0x61,
	0x79, 0x65, 0x72, 0x42, 0x72, 0x69, 0x65, 0x66, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x12,
	0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x49, 0x64, 0x12, 0x1f,
	0x0a, 0x0b, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x04, 0x52, 0x0a, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x22,
	0x73, 0x0a, 0x17, 0x42, 0x61, 0x74, 0x63, 0x68, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x42, 0x72,
	0x69, 0x65, 0x66, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x73, 0x70, 0x12, 0x20, 0x0a, 0x03, 0x72, 0x65,
	0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x03, 0x72, 0x65, 0x74, 0x12, 0x36, 0x0a, 0x0b,
	0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x42, 0x72, 0x69, 0x65, 0x66,
	0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0a, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72,
	0x49, 0x6e, 0x66, 0x6f, 0x32, 0x9a, 0x0b, 0x0a, 0x0b, 0x55, 0x73, 0x65, 0x72, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x12, 0x44, 0x0a, 0x0c, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x6c,
	0x61, 0x79, 0x65, 0x72, 0x12, 0x18, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x52, 0x70, 0x63, 0x2e, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x52, 0x65, 0x71, 0x1a, 0x18,
	0x2e, 0x75, 0x73, 0x65, 0x72, 0x52, 0x70, 0x63, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50,
	0x6c, 0x61, 0x79, 0x65, 0x72, 0x52, 0x73, 0x70, 0x22, 0x00, 0x12, 0x47, 0x0a, 0x0d, 0x47, 0x65,
	0x74, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x19, 0x2e, 0x75, 0x73,
	0x65, 0x72, 0x52, 0x70, 0x63, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x1a, 0x19, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x52, 0x70, 0x63,
	0x2e, 0x47, 0x65, 0x74, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x73,
	0x70, 0x22, 0x00, 0x12, 0x59, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72,
	0x49, 0x64, 0x42, 0x79, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x12, 0x1f, 0x2e, 0x75, 0x73, 0x65,
	0x72, 0x52, 0x70, 0x63, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x64,
	0x42, 0x79, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x1f, 0x2e, 0x75, 0x73,
	0x65, 0x72, 0x52, 0x70, 0x63, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49,
	0x64, 0x42, 0x79, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x52, 0x73, 0x70, 0x22, 0x00, 0x12, 0x50,
	0x0a, 0x10, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x1c, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x52, 0x70, 0x63, 0x2e, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71,
	0x1a, 0x1c, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x52, 0x70, 0x63, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x73, 0x70, 0x22, 0x00,
	0x12, 0x59, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x44, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1f, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x52, 0x70,
	0x63, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x44, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x1a, 0x1f, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x52,
	0x70, 0x63, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x44, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x73, 0x70, 0x22, 0x00, 0x12, 0x5c, 0x0a, 0x14, 0x47,
	0x65, 0x74, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x64, 0x42, 0x79, 0x41, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x12, 0x20, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x52, 0x70, 0x63, 0x2e, 0x47, 0x65,
	0x74, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x64, 0x42, 0x79, 0x41, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x20, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x52, 0x70, 0x63, 0x2e,
	0x47, 0x65, 0x74, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x64, 0x42, 0x79, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x52, 0x73, 0x70, 0x22, 0x00, 0x12, 0x47, 0x0a, 0x0d, 0x44, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x19, 0x2e, 0x75, 0x73, 0x65,
	0x72, 0x52, 0x70, 0x63, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x19, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x52, 0x70, 0x63, 0x2e,
	0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x73, 0x70,
	0x22, 0x00, 0x12, 0x53, 0x0a, 0x11, 0x52, 0x65, 0x61, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x41, 0x75,
	0x74, 0x68, 0x51, 0x75, 0x65, 0x72, 0x79, 0x12, 0x1d, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x52, 0x70,
	0x63, 0x2e, 0x52, 0x65, 0x61, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x41, 0x75, 0x74, 0x68, 0x51, 0x75,
	0x65, 0x72, 0x79, 0x52, 0x65, 0x71, 0x1a, 0x1d, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x52, 0x70, 0x63,
	0x2e, 0x52, 0x65, 0x61, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x41, 0x75, 0x74, 0x68, 0x51, 0x75, 0x65,
	0x72, 0x79, 0x52, 0x73, 0x70, 0x22, 0x00, 0x12, 0x44, 0x0a, 0x0c, 0x52, 0x65, 0x61, 0x6c, 0x4e,
	0x61, 0x6d, 0x65, 0x41, 0x75, 0x74, 0x68, 0x12, 0x18, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x52, 0x70,
	0x63, 0x2e, 0x52, 0x65, 0x61, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x41, 0x75, 0x74, 0x68, 0x52, 0x65,
	0x71, 0x1a, 0x18, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x52, 0x70, 0x63, 0x2e, 0x52, 0x65, 0x61, 0x6c,
	0x4e, 0x61, 0x6d, 0x65, 0x41, 0x75, 0x74, 0x68, 0x52, 0x73, 0x70, 0x22, 0x00, 0x12, 0x4a, 0x0a,
	0x0e, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x41, 0x67, 0x65, 0x51, 0x75, 0x65, 0x72, 0x79, 0x12,
	0x1a, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x52, 0x70, 0x63, 0x2e, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72,
	0x41, 0x67, 0x65, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x65, 0x71, 0x1a, 0x1a, 0x2e, 0x75, 0x73,
	0x65, 0x72, 0x52, 0x70, 0x63, 0x2e, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x41, 0x67, 0x65, 0x51,
	0x75, 0x65, 0x72, 0x79, 0x52, 0x73, 0x70, 0x22, 0x00, 0x12, 0x50, 0x0a, 0x10, 0x50, 0x6c, 0x61,
	0x79, 0x65, 0x72, 0x4d, 0x75, 0x6c, 0x74, 0x69, 0x51, 0x75, 0x65, 0x72, 0x79, 0x12, 0x1c, 0x2e,
	0x75, 0x73, 0x65, 0x72, 0x52, 0x70, 0x63, 0x2e, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x4d, 0x75,
	0x6c, 0x74, 0x69, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x65, 0x71, 0x1a, 0x1c, 0x2e, 0x75, 0x73,
	0x65, 0x72, 0x52, 0x70, 0x63, 0x2e, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x4d, 0x75, 0x6c, 0x74,
	0x69, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x73, 0x70, 0x22, 0x00, 0x12, 0x47, 0x0a, 0x11, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x4c, 0x6f, 0x67, 0x69, 0x6e,
	0x12, 0x17, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x52, 0x70, 0x63, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x52, 0x65, 0x71, 0x1a, 0x17, 0x2e, 0x75, 0x73, 0x65, 0x72,
	0x52, 0x70, 0x63, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x52,
	0x73, 0x70, 0x22, 0x00, 0x12, 0x4d, 0x0a, 0x0f, 0x42, 0x61, 0x74, 0x63, 0x68, 0x50, 0x6c, 0x61,
	0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1b, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x52, 0x70,
	0x63, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x65, 0x71, 0x1a, 0x1b, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x52, 0x70, 0x63, 0x2e, 0x42,
	0x61, 0x74, 0x63, 0x68, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x73,
	0x70, 0x22, 0x00, 0x12, 0x59, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72,
	0x49, 0x64, 0x42, 0x79, 0x4f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x12, 0x1f, 0x2e, 0x75, 0x73, 0x65,
	0x72, 0x52, 0x70, 0x63, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x64,
	0x42, 0x79, 0x4f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x52, 0x65, 0x71, 0x1a, 0x1f, 0x2e, 0x75, 0x73,
	0x65, 0x72, 0x52, 0x70, 0x63, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49,
	0x64, 0x42, 0x79, 0x4f, 0x70, 0x65, 0x6e, 0x49, 0x64, 0x52, 0x73, 0x70, 0x22, 0x00, 0x12, 0x5f,
	0x0a, 0x15, 0x51, 0x75, 0x65, 0x72, 0x79, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x45, 0x78, 0x74,
	0x65, 0x6e, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x21, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x52, 0x70,
	0x63, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x45, 0x78, 0x74,
	0x65, 0x6e, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x1a, 0x21, 0x2e, 0x75, 0x73, 0x65,
	0x72, 0x52, 0x70, 0x63, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72,
	0x45, 0x78, 0x74, 0x65, 0x6e, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x73, 0x70, 0x22, 0x00, 0x12,
	0x62, 0x0a, 0x16, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x45,
	0x78, 0x74, 0x65, 0x6e, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x22, 0x2e, 0x75, 0x73, 0x65, 0x72,
	0x52, 0x70, 0x63, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72,
	0x45, 0x78, 0x74, 0x65, 0x6e, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x1a, 0x22, 0x2e,
	0x75, 0x73, 0x65, 0x72, 0x52, 0x70, 0x63, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x6c,
	0x61, 0x79, 0x65, 0x72, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x73,
	0x70, 0x22, 0x00, 0x12, 0x5c, 0x0a, 0x14, 0x42, 0x61, 0x74, 0x63, 0x68, 0x50, 0x6c, 0x61, 0x79,
	0x65, 0x72, 0x42, 0x72, 0x69, 0x65, 0x66, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x20, 0x2e, 0x75, 0x73,
	0x65, 0x72, 0x52, 0x70, 0x63, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x50, 0x6c, 0x61, 0x79, 0x65,
	0x72, 0x42, 0x72, 0x69, 0x65, 0x66, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x1a, 0x20, 0x2e,
	0x75, 0x73, 0x65, 0x72, 0x52, 0x70, 0x63, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x50, 0x6c, 0x61,
	0x79, 0x65, 0x72, 0x42, 0x72, 0x69, 0x65, 0x66, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x73, 0x70, 0x22,
	0x00, 0x42, 0x0b, 0x5a, 0x09, 0x2e, 0x3b, 0x75, 0x73, 0x65, 0x72, 0x52, 0x70, 0x63, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_userrpc_userrpc_proto_rawDescOnce sync.Once
	file_userrpc_userrpc_proto_rawDescData = file_userrpc_userrpc_proto_rawDesc
)

func file_userrpc_userrpc_proto_rawDescGZIP() []byte {
	file_userrpc_userrpc_proto_rawDescOnce.Do(func() {
		file_userrpc_userrpc_proto_rawDescData = protoimpl.X.CompressGZIP(file_userrpc_userrpc_proto_rawDescData)
	})
	return file_userrpc_userrpc_proto_rawDescData
}

var file_userrpc_userrpc_proto_msgTypes = make([]protoimpl.MessageInfo, 37)
var file_userrpc_userrpc_proto_goTypes = []interface{}{
	(*CreatePlayerReq)(nil),           // 0: userRpc.CreatePlayerReq
	(*CreatePlayerRsp)(nil),           // 1: userRpc.CreatePlayerRsp
	(*GetPlayerInfoReq)(nil),          // 2: userRpc.GetPlayerInfoReq
	(*GetPlayerInfoRsp)(nil),          // 3: userRpc.GetPlayerInfoRsp
	(*GetPlayerIdByDeviceReq)(nil),    // 4: userRpc.GetPlayerIdByDeviceReq
	(*GetPlayerIdByDeviceRsp)(nil),    // 5: userRpc.GetPlayerIdByDeviceRsp
	(*UpdatePlayerInfoReq)(nil),       // 6: userRpc.UpdatePlayerInfoReq
	(*UpdatePlayerInfoRsp)(nil),       // 7: userRpc.UpdatePlayerInfoRsp
	(*GetPlayerDeviceInfoReq)(nil),    // 8: userRpc.GetPlayerDeviceInfoReq
	(*GetPlayerDeviceInfoRsp)(nil),    // 9: userRpc.GetPlayerDeviceInfoRsp
	(*GetPlayerIdByAccountReq)(nil),   // 10: userRpc.GetPlayerIdByAccountReq
	(*GetPlayerIdByAccountRsp)(nil),   // 11: userRpc.GetPlayerIdByAccountRsp
	(*DeleteAccountReq)(nil),          // 12: userRpc.DeleteAccountReq
	(*DeleteAccountRsp)(nil),          // 13: userRpc.DeleteAccountRsp
	(*RealNameAuthQueryReq)(nil),      // 14: userRpc.RealNameAuthQueryReq
	(*RealNameAuthQueryRsp)(nil),      // 15: userRpc.RealNameAuthQueryRsp
	(*RealNameAuthReq)(nil),           // 16: userRpc.RealNameAuthReq
	(*RealNameAuthRsp)(nil),           // 17: userRpc.RealNameAuthRsp
	(*PlayerAgeQueryReq)(nil),         // 18: userRpc.PlayerAgeQueryReq
	(*PlayerAgeQueryRsp)(nil),         // 19: userRpc.PlayerAgeQueryRsp
	(*PlayerMultiQueryReq)(nil),       // 20: userRpc.PlayerMultiQueryReq
	(*PlayerMultiQueryRsp)(nil),       // 21: userRpc.PlayerMultiQueryRsp
	(*UpdateLoginReq)(nil),            // 22: userRpc.UpdateLoginReq
	(*UpdateLoginRsp)(nil),            // 23: userRpc.UpdateLoginRsp
	(*BatchPlayerInfoReq)(nil),        // 24: userRpc.BatchPlayerInfoReq
	(*BatchPlayerInfoRsp)(nil),        // 25: userRpc.BatchPlayerInfoRsp
	(*GetPlayerIdByOpenIdReq)(nil),    // 26: userRpc.GetPlayerIdByOpenIdReq
	(*GetPlayerIdByOpenIdRsp)(nil),    // 27: userRpc.GetPlayerIdByOpenIdRsp
	(*QueryPlayerExtendInfoReq)(nil),  // 28: userRpc.QueryPlayerExtendInfoReq
	(*QueryPlayerExtendInfoRsp)(nil),  // 29: userRpc.QueryPlayerExtendInfoRsp
	(*UpdatePlayerExtendInfoReq)(nil), // 30: userRpc.UpdatePlayerExtendInfoReq
	(*UpdatePlayerExtendInfoRsp)(nil), // 31: userRpc.UpdatePlayerExtendInfoRsp
	(*BatchPlayerBriefInfoReq)(nil),   // 32: userRpc.BatchPlayerBriefInfoReq
	(*BatchPlayerBriefInfoRsp)(nil),   // 33: userRpc.BatchPlayerBriefInfoRsp
	nil,                               // 34: userRpc.UpdatePlayerInfoReq.UpdateMapEntry
	nil,                               // 35: userRpc.UpdatePlayerInfoRsp.UpdateMapEntry
	nil,                               // 36: userRpc.PlayerMultiQueryRsp.PlayerInfoEntry
	(common.CHANNEL_TYPE)(0),          // 37: common.CHANNEL_TYPE
	(*common.DeviceInfo)(nil),         // 38: common.DeviceInfo
	(common.NETWORK_TYPE)(0),          // 39: common.NETWORK_TYPE
	(common.PLATFORM_TYPE)(0),         // 40: common.PLATFORM_TYPE
	(*common.AccountInfo)(nil),        // 41: common.AccountInfo
	(common.LOGIN_TYPE)(0),            // 42: common.LOGIN_TYPE
	(*common.ThirdLoginInfo)(nil),     // 43: common.ThirdLoginInfo
	(*common.RichUserInfo)(nil),       // 44: common.RichUserInfo
	(*common.Result)(nil),             // 45: common.Result
	(*common.PlayerRealNameAuth)(nil), // 46: common.PlayerRealNameAuth
	(common.USER_AGE)(0),              // 47: common.USER_AGE
	(*common.PaginationReq)(nil),      // 48: common.PaginationReq
	(*common.ExtendUserInfo)(nil),     // 49: common.ExtendUserInfo
	(*common.BriefUserInfo)(nil),      // 50: common.BriefUserInfo
}
var file_userrpc_userrpc_proto_depIdxs = []int32{
	37, // 0: userRpc.CreatePlayerReq.channel_id:type_name -> common.CHANNEL_TYPE
	38, // 1: userRpc.CreatePlayerReq.device_info:type_name -> common.DeviceInfo
	39, // 2: userRpc.CreatePlayerReq.network:type_name -> common.NETWORK_TYPE
	40, // 3: userRpc.CreatePlayerReq.platform:type_name -> common.PLATFORM_TYPE
	41, // 4: userRpc.CreatePlayerReq.account_info:type_name -> common.AccountInfo
	42, // 5: userRpc.CreatePlayerReq.create_type:type_name -> common.LOGIN_TYPE
	43, // 6: userRpc.CreatePlayerReq.third_info:type_name -> common.ThirdLoginInfo
	44, // 7: userRpc.CreatePlayerRsp.rich_user_info:type_name -> common.RichUserInfo
	44, // 8: userRpc.GetPlayerInfoRsp.rich_user_info:type_name -> common.RichUserInfo
	34, // 9: userRpc.UpdatePlayerInfoReq.update_map:type_name -> userRpc.UpdatePlayerInfoReq.UpdateMapEntry
	45, // 10: userRpc.UpdatePlayerInfoRsp.ret:type_name -> common.Result
	35, // 11: userRpc.UpdatePlayerInfoRsp.update_map:type_name -> userRpc.UpdatePlayerInfoRsp.UpdateMapEntry
	38, // 12: userRpc.GetPlayerDeviceInfoRsp.device_info:type_name -> common.DeviceInfo
	41, // 13: userRpc.GetPlayerIdByAccountReq.account_info:type_name -> common.AccountInfo
	45, // 14: userRpc.DeleteAccountRsp.ret:type_name -> common.Result
	45, // 15: userRpc.RealNameAuthQueryRsp.ret:type_name -> common.Result
	46, // 16: userRpc.RealNameAuthReq.auth_info:type_name -> common.PlayerRealNameAuth
	45, // 17: userRpc.RealNameAuthRsp.ret:type_name -> common.Result
	45, // 18: userRpc.PlayerAgeQueryRsp.ret:type_name -> common.Result
	47, // 19: userRpc.PlayerAgeQueryRsp.age:type_name -> common.USER_AGE
	45, // 20: userRpc.PlayerMultiQueryRsp.ret:type_name -> common.Result
	36, // 21: userRpc.PlayerMultiQueryRsp.player_info:type_name -> userRpc.PlayerMultiQueryRsp.PlayerInfoEntry
	38, // 22: userRpc.UpdateLoginReq.device_info:type_name -> common.DeviceInfo
	45, // 23: userRpc.UpdateLoginRsp.ret:type_name -> common.Result
	48, // 24: userRpc.BatchPlayerInfoReq.pagination:type_name -> common.PaginationReq
	45, // 25: userRpc.BatchPlayerInfoRsp.ret:type_name -> common.Result
	44, // 26: userRpc.BatchPlayerInfoRsp.player_info:type_name -> common.RichUserInfo
	42, // 27: userRpc.GetPlayerIdByOpenIdReq.login_type:type_name -> common.LOGIN_TYPE
	45, // 28: userRpc.QueryPlayerExtendInfoRsp.ret:type_name -> common.Result
	49, // 29: userRpc.QueryPlayerExtendInfoRsp.extend_info:type_name -> common.ExtendUserInfo
	49, // 30: userRpc.UpdatePlayerExtendInfoReq.extend_info:type_name -> common.ExtendUserInfo
	45, // 31: userRpc.UpdatePlayerExtendInfoRsp.ret:type_name -> common.Result
	45, // 32: userRpc.BatchPlayerBriefInfoRsp.ret:type_name -> common.Result
	50, // 33: userRpc.BatchPlayerBriefInfoRsp.player_info:type_name -> common.BriefUserInfo
	44, // 34: userRpc.PlayerMultiQueryRsp.PlayerInfoEntry.value:type_name -> common.RichUserInfo
	0,  // 35: userRpc.UserService.CreatePlayer:input_type -> userRpc.CreatePlayerReq
	2,  // 36: userRpc.UserService.GetPlayerInfo:input_type -> userRpc.GetPlayerInfoReq
	4,  // 37: userRpc.UserService.GetPlayerIdByDevice:input_type -> userRpc.GetPlayerIdByDeviceReq
	6,  // 38: userRpc.UserService.UpdatePlayerInfo:input_type -> userRpc.UpdatePlayerInfoReq
	8,  // 39: userRpc.UserService.GetPlayerDeviceInfo:input_type -> userRpc.GetPlayerDeviceInfoReq
	10, // 40: userRpc.UserService.GetPlayerIdByAccount:input_type -> userRpc.GetPlayerIdByAccountReq
	12, // 41: userRpc.UserService.DeleteAccount:input_type -> userRpc.DeleteAccountReq
	14, // 42: userRpc.UserService.RealNameAuthQuery:input_type -> userRpc.RealNameAuthQueryReq
	16, // 43: userRpc.UserService.RealNameAuth:input_type -> userRpc.RealNameAuthReq
	18, // 44: userRpc.UserService.PlayerAgeQuery:input_type -> userRpc.PlayerAgeQueryReq
	20, // 45: userRpc.UserService.PlayerMultiQuery:input_type -> userRpc.PlayerMultiQueryReq
	22, // 46: userRpc.UserService.UpdatePlayerLogin:input_type -> userRpc.UpdateLoginReq
	24, // 47: userRpc.UserService.BatchPlayerInfo:input_type -> userRpc.BatchPlayerInfoReq
	26, // 48: userRpc.UserService.GetPlayerIdByOpenId:input_type -> userRpc.GetPlayerIdByOpenIdReq
	28, // 49: userRpc.UserService.QueryPlayerExtendInfo:input_type -> userRpc.QueryPlayerExtendInfoReq
	30, // 50: userRpc.UserService.UpdatePlayerExtendInfo:input_type -> userRpc.UpdatePlayerExtendInfoReq
	32, // 51: userRpc.UserService.BatchPlayerBriefInfo:input_type -> userRpc.BatchPlayerBriefInfoReq
	1,  // 52: userRpc.UserService.CreatePlayer:output_type -> userRpc.CreatePlayerRsp
	3,  // 53: userRpc.UserService.GetPlayerInfo:output_type -> userRpc.GetPlayerInfoRsp
	5,  // 54: userRpc.UserService.GetPlayerIdByDevice:output_type -> userRpc.GetPlayerIdByDeviceRsp
	7,  // 55: userRpc.UserService.UpdatePlayerInfo:output_type -> userRpc.UpdatePlayerInfoRsp
	9,  // 56: userRpc.UserService.GetPlayerDeviceInfo:output_type -> userRpc.GetPlayerDeviceInfoRsp
	11, // 57: userRpc.UserService.GetPlayerIdByAccount:output_type -> userRpc.GetPlayerIdByAccountRsp
	13, // 58: userRpc.UserService.DeleteAccount:output_type -> userRpc.DeleteAccountRsp
	15, // 59: userRpc.UserService.RealNameAuthQuery:output_type -> userRpc.RealNameAuthQueryRsp
	17, // 60: userRpc.UserService.RealNameAuth:output_type -> userRpc.RealNameAuthRsp
	19, // 61: userRpc.UserService.PlayerAgeQuery:output_type -> userRpc.PlayerAgeQueryRsp
	21, // 62: userRpc.UserService.PlayerMultiQuery:output_type -> userRpc.PlayerMultiQueryRsp
	23, // 63: userRpc.UserService.UpdatePlayerLogin:output_type -> userRpc.UpdateLoginRsp
	25, // 64: userRpc.UserService.BatchPlayerInfo:output_type -> userRpc.BatchPlayerInfoRsp
	27, // 65: userRpc.UserService.GetPlayerIdByOpenId:output_type -> userRpc.GetPlayerIdByOpenIdRsp
	29, // 66: userRpc.UserService.QueryPlayerExtendInfo:output_type -> userRpc.QueryPlayerExtendInfoRsp
	31, // 67: userRpc.UserService.UpdatePlayerExtendInfo:output_type -> userRpc.UpdatePlayerExtendInfoRsp
	33, // 68: userRpc.UserService.BatchPlayerBriefInfo:output_type -> userRpc.BatchPlayerBriefInfoRsp
	52, // [52:69] is the sub-list for method output_type
	35, // [35:52] is the sub-list for method input_type
	35, // [35:35] is the sub-list for extension type_name
	35, // [35:35] is the sub-list for extension extendee
	0,  // [0:35] is the sub-list for field type_name
}

func init() { file_userrpc_userrpc_proto_init() }
func file_userrpc_userrpc_proto_init() {
	if File_userrpc_userrpc_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_userrpc_userrpc_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreatePlayerReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_userrpc_userrpc_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreatePlayerRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_userrpc_userrpc_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPlayerInfoReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_userrpc_userrpc_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPlayerInfoRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_userrpc_userrpc_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPlayerIdByDeviceReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_userrpc_userrpc_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPlayerIdByDeviceRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_userrpc_userrpc_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdatePlayerInfoReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_userrpc_userrpc_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdatePlayerInfoRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_userrpc_userrpc_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPlayerDeviceInfoReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_userrpc_userrpc_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPlayerDeviceInfoRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_userrpc_userrpc_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPlayerIdByAccountReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_userrpc_userrpc_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPlayerIdByAccountRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_userrpc_userrpc_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteAccountReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_userrpc_userrpc_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteAccountRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_userrpc_userrpc_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RealNameAuthQueryReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_userrpc_userrpc_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RealNameAuthQueryRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_userrpc_userrpc_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RealNameAuthReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_userrpc_userrpc_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RealNameAuthRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_userrpc_userrpc_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PlayerAgeQueryReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_userrpc_userrpc_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PlayerAgeQueryRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_userrpc_userrpc_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PlayerMultiQueryReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_userrpc_userrpc_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PlayerMultiQueryRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_userrpc_userrpc_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateLoginReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_userrpc_userrpc_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateLoginRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_userrpc_userrpc_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchPlayerInfoReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_userrpc_userrpc_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchPlayerInfoRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_userrpc_userrpc_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPlayerIdByOpenIdReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_userrpc_userrpc_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPlayerIdByOpenIdRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_userrpc_userrpc_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryPlayerExtendInfoReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_userrpc_userrpc_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryPlayerExtendInfoRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_userrpc_userrpc_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdatePlayerExtendInfoReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_userrpc_userrpc_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdatePlayerExtendInfoRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_userrpc_userrpc_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchPlayerBriefInfoReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_userrpc_userrpc_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchPlayerBriefInfoRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_userrpc_userrpc_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   37,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_userrpc_userrpc_proto_goTypes,
		DependencyIndexes: file_userrpc_userrpc_proto_depIdxs,
		MessageInfos:      file_userrpc_userrpc_proto_msgTypes,
	}.Build()
	File_userrpc_userrpc_proto = out.File
	file_userrpc_userrpc_proto_rawDesc = nil
	file_userrpc_userrpc_proto_goTypes = nil
	file_userrpc_userrpc_proto_depIdxs = nil
}
