// File: config_test.go
package consul_config

import (
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"github.com/sirupsen/logrus"
	"github.com/spf13/viper"
	"testing"

	"github.com/hashicorp/consul/api"
)

var testClient *api.Client

func put(k, v string) {
	_, err := testClient.KV().Put(&api.KVPair{
		Key:   k,
		Value: []byte(v),
	}, &api.WriteOptions{})
	if err != nil {
		return
	}
}

func init() {
	viper.SetDefault("consul_addr", "************:8500")

	// var err error
	// testClient, err = api.NewClient(&api.Config{
	// 	Address: "localhost:8500",
	// })
	// if err != nil {
	// 	panic(err)
	// }
	//
	// _, err = testClient.KV().DeleteTree("config/", &api.WriteOptions{})
	// if err != nil {
	// 	return
	// }

	logrus.SetLevel(logrus.DebugLevel)

}

func TestOpt2String(t *testing.T) {
	opt := &options{
		jType:   nil,
		product: 1,
		channel: 2,
		lang:    commonPB.LANGUAGE_TYPE_LT_ZH_CN,
	}
	t.Log(opt.String())
}

type Foo struct {
	Bar string `json:"bar"`
	Baz string `json:"baz"`
}

// 测试精确匹配
func TestGetConfigExactlyMatch(t *testing.T) {
	// foo := GetConfig("foo", WithJSON(Foo{}),
	// 	WithProduct(9999),
	// 	WithProvince(51)).(*Foo)
	// assert.Equal(t, "123", foo.Bar)
	// assert.Equal(t, "156", foo.Baz)
}

func TestGetOption(t *testing.T) {
	key := "app_update_info"
	ret := GetInstance().GetConfig(key)
	t.Log(ret)
}

func TestGetOptionChannel(t *testing.T) {
	key := "app_update_info"
	ret := GetInstance().GetConfig(key, WithChannel(1002))
	t.Log(ret)
}
