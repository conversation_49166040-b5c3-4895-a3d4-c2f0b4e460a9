package protox

import (
	"encoding/json"
	"errors"
	"strings"

	"git.keepfancy.xyz/back-end/frameworks/dict"
	"git.keepfancy.xyz/back-end/frameworks/lib/errorx"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/frameworks/kit/transport"
	"google.golang.org/protobuf/proto"
)

// AppendResp 添加回包
func AppendResp(old []transport.ResponseMsg, msgID commonPB.MsgID, body proto.Message) (result []transport.ResponseMsg) {
	result = append(old, transport.ResponseMsg{
		MsgID: uint32(msgID),
		Body:  body,
	})
	return
}

// PackRespMsg 组合transport.ResponseMsg
func PackRespMsg(msgID commonPB.MsgID, body proto.Message) *transport.ResponseMsg {
	return &transport.ResponseMsg{
		MsgID: uint32(msgID),
		Body:  body,
	}
}

// DefaultResult 创建缺省Result
func DefaultResult() *commonPB.Result {
	return &commonPB.Result{
		Code: commonPB.ErrCode_ERR_FAIL,
		Desc: commonPB.ErrCode_ERR_FAIL.String(),
	}
}

// FillCodeResult 填充Ret，desc和code一致
func FillCodeResult(ec commonPB.ErrCode, extraDesc ...string) *commonPB.Result {
	var extendedDesc strings.Builder
	if len(extraDesc) > 0 {
		// 将额外的描述合并，用逗号分隔
		for i, d := range extraDesc {
			if i > 0 {
				extendedDesc.WriteString(", ")
			} else {
				extendedDesc.WriteString(": ")
			}
			extendedDesc.WriteString(d)
		}
	}

	fullDesc := ec.String() + extendedDesc.String()

	return &commonPB.Result{
		Code: ec,
		Desc: fullDesc,
	}
}

// ErrorCode 错误码编码
func ErrorCode(eCode commonPB.ErrCode) *int32 {
	return proto.Int32(int32(eCode))
}

// Error 错误码编码内容
func Error(msg string) *string {
	return proto.String(msg)
}

// PB2Error PB格式错误编码转error
func PB2Error(msg commonPB.ErrCode, extraDesc ...string) error {
	return errors.New(msg.String() + dict.SysSymbolColon + strings.Join(extraDesc, ", "))
}

func ToJson(pb interface{}) string {
	jsonBytes, err := json.Marshal(pb)
	if err != nil {
		return ""
	}
	return string(jsonBytes)
}

func FillErrResult(err error) *commonPB.Result {
	if err == nil {
		return &commonPB.Result{
			Code: commonPB.ErrCode_ERR_SUCCESS,
		}
	}
	// 目前只解第一层
	if x, ok := err.(interface{ GetCode() int }); ok {
		return &commonPB.Result{
			Code: commonPB.ErrCode(x.GetCode()),
			Desc: err.Error(),
		}
	}

	return &commonPB.Result{
		Code: commonPB.ErrCode_ERR_FAIL,
		Desc: err.Error(),
	}
}

func CodeError(code commonPB.ErrCode, msg ...string) error {
	err := &errorx.ErrCode{}
	err.SetCode(int(code))

	if len(msg) == 0 {
		err.SetMsg(code.String())
	} else {
		err.SetMsg(strings.Join(msg, ","))
	}
	return err
}
